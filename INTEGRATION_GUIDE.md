# BlackHawk + Isomonodromic Method 整合指南

## 概述

本项目整合了BlackHawk v2.3（霍金辐射计算）和Isomonodromic Method（超辐射QNM计算），构建了一个统一的黑洞演化数值程序，可以同时考虑霍金辐射和超辐射效应。

## 项目结构

```
pbh-evo/
├── src/                          # 新的统一代码
│   ├── qnm_interface.c          # Julia-C接口
│   ├── superradiance.c          # 超辐射计算
│   ├── include/                 # 头文件
│   │   ├── qnm_interface.h
│   │   └── superradiance.h
│   ├── test_qnm.c              # 测试程序
│   └── Makefile
├── external/                    # 外部代码
│   ├── blackhawk_v2.3/         # BlackHawk代码
│   └── Isomonodromic Method/   # QNM计算代码
└── unified_evolution_plan.md    # 详细整合计划
```

## 核心功能

### 1. 霍金辐射（来自BlackHawk）
- 计算各种粒子的发射谱
- 支持Kerr、带电、高维黑洞
- 包含完整的粒子物理过程

### 2. 超辐射（新增功能）
- 通过Isomonodromic Method计算QNM频率
- 判断超辐射条件：ω_R < m·Ω_H
- 计算质量和角动量提取率

### 3. 统一演化
- 同时考虑两种效应：
  ```
  dM/dt = dM/dt_Hawking + dM/dt_Superradiance
  da/dt = da/dt_Hawking + da/dt_Superradiance
  ```
- 自适应时间步长
- 能量守恒检验

## 使用方法

### 编译

```bash
cd src
make clean
make all
```

### 运行测试

```bash
# 测试QNM接口
./test_qnm

# 测试超辐射计算
./test_super
```

### 参数设置

在参数文件中添加超辐射相关参数：

```
# 超辐射参数
enable_superradiance = 1    # 开启超辐射
scalar_mass = 1e-12        # 标量场质量 (eV)
l_max = 3                  # 最大角量子数
m_max = 3                  # 最大磁量子数
initial_cloud_fraction = 1e-6  # 初始云质量分数
```

## 物理考虑

### 1. 时间尺度
- 霍金辐射：t_H ~ M³/M_p⁴
- 超辐射：t_SR ~ 1/(μ·α⁷)，其中α = GM·μ

### 2. 主导机制
- 高温黑洞（小质量）：霍金辐射主导
- 低温旋转黑洞：可能超辐射主导
- 需要仔细处理两者竞争

### 3. 数值稳定性
- 超辐射时标可能远长于霍金辐射
- 使用自适应时间步长
- 考虑使用隐式方法

## 技术要点

### 1. Julia-C接口
- 通过系统调用或Julia C API
- 结果缓存以提高效率
- 考虑预计算查找表

### 2. 并行化
- 不同(l,m,n)模式可并行计算
- OpenMP并行化演化循环
- MPI用于参数扫描

### 3. 验证
- 与文献QNM值对比
- 能量守恒检验
- 极限情况测试

## 后续开发

### 第一阶段（已完成）
- [x] 基础接口设计
- [x] QNM计算接口
- [x] 超辐射率计算

### 第二阶段（进行中）
- [ ] 整合演化方程
- [ ] 实现自适应步长
- [ ] 完整测试套件

### 第三阶段（计划中）
- [ ] 优化性能
- [ ] 添加更多物理效应
- [ ] 图形化界面

## 参考文献

1. BlackHawk: Arbey & Auffinger, Eur. Phys. J. C79 (2019) 693
2. Isomonodromic Method: [arXiv:2407.20850], [arXiv:2408.13964]
3. Superradiance: Arvanitaki & Dubovsky, Phys. Rev. D83 (2011) 044026

## 联系方式

如有问题或建议，请联系项目维护者。 