(* Content-type: application/vnd.wolfram.mathematica *)

(*** Wolfram Notebook File ***)
(* http://www.wolfram.com/nb *)

(* CreatedBy='Mathematica 12.1' *)

(*CacheID: 234*)
(* Internal cache information:
NotebookFileLineBreakTest
NotebookFileLineBreakTest
NotebookDataPosition[       158,          7]
NotebookDataLength[    129474,       3006]
NotebookOptionsPosition[    119694,       2852]
NotebookOutlinePosition[    120210,       2871]
CellTagsIndexPosition[    120167,       2868]
WindowFrame->Normal*)

(* Beginning of Notebook Content *)
Notebook[{
Cell[BoxData[{
 StyleBox[
  RowBox[{
   RowBox[{
    RowBox[{"In", " ", "this", " ", "notebook"}], ",", 
    RowBox[{
    "we", " ", "derive", " ", "the", " ", "expansion", " ", "for", " ", "the",
      " ", "angular", " ", "eigenvalue", " ", "and", " ", "demonstrate", " ", 
     "how", " ", "the", " ", "analytic", " ", "expression", " ", "for", " ", 
     "the", " ", "frequency", " ", "in", " ", "the"}]}], " "}], 
  "Subsubsection",
  FontSize->14,
  FontColor->GrayLevel[0]], "\[IndentingNewLine]", 
 StyleBox[
  RowBox[{
   RowBox[{
    RowBox[{
     RowBox[{"extremal", " ", "limit", 
      StyleBox[
       StyleBox[
        RowBox[{" ", 
         StyleBox[
          StyleBox[" ", "Subsubsection",
           FontSize->14,
           FontColor->GrayLevel[0]], "Subsubsection",
          FontSize->14,
          FontColor->GrayLevel[0]]}]], "Subsubsection",
       FontSize->14,
       FontColor->GrayLevel[0]], "a"}], "\[RightArrow]", 
     RowBox[{"M", " ", "is", " ", 
      RowBox[{"obtained", ".", 
       StyleBox[
        StyleBox[
         StyleBox[
          StyleBox[" ", "Subsubsection",
           FontSize->14,
           FontColor->GrayLevel[0]], "Subsubsection",
          FontSize->14,
          FontColor->GrayLevel[0]], "Subsubsection",
         FontSize->14,
         FontColor->GrayLevel[0]], "Subsubsection",
        FontSize->14,
        FontColor->GrayLevel[0]], "Additionally"}]}]}], 
    StyleBox[
     StyleBox[
      StyleBox[
       StyleBox[",", "Subsubsection",
        FontSize->14,
        FontColor->GrayLevel[0]], "Subsubsection",
       FontSize->14,
       FontColor->GrayLevel[0]], "Subsubsection",
      FontSize->14,
      FontColor->GrayLevel[0]], "Subsubsection",
     FontSize->14,
     FontColor->GrayLevel[0]], 
    StyleBox[
     StyleBox[" ", "Subsubsection",
      FontSize->14,
      FontColor->GrayLevel[0]], "Subsubsection",
     FontSize->14,
     FontColor->GrayLevel[0]], 
    RowBox[{
    "we", " ", "confirm", " ", "that", " ", "the", " ", "derived", " ", 
     "expression", " ", "aligns", " ", "with", " ", "the", " ", "numerical", 
     " ", "results", " ", "produced", " ", "using"}]}], " "}], "Subsubsection",
  FontSize->14,
  FontColor->GrayLevel[0]], "\[IndentingNewLine]", 
 StyleBox[
  RowBox[{"the", " ", "Isomonodromy", " ", 
   RowBox[{"Method", "."}]}], "Subsubsection",
  FontSize->14,
  FontColor->GrayLevel[0]]}], "Input",
 CellChangeTimes->{{3.904235827418623*^9, 3.9042358700147247`*^9}, {
   3.938179341176466*^9, 3.938179361995583*^9}, {3.938181532986641*^9, 
   3.938181552024914*^9}, {3.938193802312963*^9, 3.9381938281554403`*^9}, {
   3.938194681493558*^9, 3.93819468343463*^9}, {3.938200637500061*^9, 
   3.938200637996407*^9}, 3.938202194555341*^9, {3.938202909028026*^9, 
   3.938202913686039*^9}, {3.938203107682745*^9, 3.93820311064091*^9}, {
   3.9382031816599407`*^9, 3.938203182079851*^9}, {3.9382514018029413`*^9, 
   3.938251403080624*^9}, {3.938263930456387*^9, 3.9382639868262587`*^9}, {
   3.938264024106894*^9, 3.938264088781068*^9}, {3.938270816447152*^9, 
   3.9382709212123737`*^9}, {3.9382710015107927`*^9, 3.9382710731414833`*^9}},
 
 TextJustification->1.,
 Background->GrayLevel[
  0.85],ExpressionUUID->"3c354bc7-bab1-421e-9d1b-5127297eddd2"],

Cell[CellGroupData[{

Cell["Angular Eigenvalue Expansion:", "Subsubsection",
 CellChangeTimes->{{3.901785058772934*^9, 3.9017850634007063`*^9}, {
  3.938180253909547*^9, 
  3.938180283781019*^9}},ExpressionUUID->"e3bc185e-a009-4831-b51c-\
0b71b7ed0db5"],

Cell["\<\
By making use of the accessory parameter expansion for small values of t0 \
given in 2408.13964, we can derive the expansion for the angular eigenvalue \
\[Lambda]. To do this, we utilize the quantization condition for the \
monodromy parameter \[Sigma] which is derived directly from the connection \
matrix between solutions for the confluent Heun equation. Based on this, ones \
\[Sigma] = l + 1, where l is the angular momentum number.\
\>", "Text",
 CellChangeTimes->{{3.938180285401119*^9, 3.938180410927924*^9}, {
   3.938180490794709*^9, 3.93818062945115*^9}, {3.938180758441063*^9, 
   3.938180768332391*^9}, {3.938181466888914*^9, 3.938181469311522*^9}, {
   3.938181507724976*^9, 3.938181513002969*^9}, {3.938194678461656*^9, 
   3.9381946798735332`*^9}, {3.9382022747357073`*^9, 3.938202277023535*^9}, {
   3.9382023999464417`*^9, 3.938202400457551*^9}, {3.938203124414137*^9, 
   3.938203127989369*^9}, {3.93820387134199*^9, 3.938203888512754*^9}, 
   3.9382639966686563`*^9, {3.9382710836217527`*^9, 3.93827108801256*^9}, {
   3.938272971570373*^9, 3.938272976514618*^9}},
 TextJustification->
  0.75,ExpressionUUID->"5424cdb5-60be-413f-8837-795a40332f24"],

Cell[BoxData[
 RowBox[{
  RowBox[{"eqnK0", "[", 
   RowBox[{
   "\[Theta]0_", ",", " ", "\[Theta]t_", ",", " ", "\[Theta]\[Prime]_", ",", 
    " ", "\[Sigma]_", ",", "t0_"}], "]"}], ":=", 
  RowBox[{
   RowBox[{
    RowBox[{"-", 
     FractionBox["1", "4"]}], " ", 
    RowBox[{"(", 
     RowBox[{"\[Theta]\[Prime]", "-", "\[Sigma]"}], ")"}], " ", 
    RowBox[{"(", 
     RowBox[{
      RowBox[{"-", "2"}], "+", "\[Theta]\[Prime]", "+", "\[Sigma]"}], ")"}]}],
    "+", 
   FractionBox[
    RowBox[{"t0", 
     RowBox[{"(", 
      RowBox[{
       RowBox[{
        RowBox[{"-", 
         SuperscriptBox["\[Theta]0", "2"]}], " ", "\[Theta]\[Prime]"}], "+", 
       RowBox[{
        SuperscriptBox["\[Theta]t", "2"], " ", "\[Theta]\[Prime]"}], "+", 
       RowBox[{"4", " ", "\[Sigma]"}], "-", 
       RowBox[{"4", " ", "\[Theta]t", " ", "\[Sigma]"}], "-", 
       RowBox[{"2", " ", "\[Theta]\[Prime]", " ", "\[Sigma]"}], "-", 
       RowBox[{"2", " ", 
        SuperscriptBox["\[Sigma]", "2"]}], "+", 
       RowBox[{"2", " ", "\[Theta]t", " ", 
        SuperscriptBox["\[Sigma]", "2"]}], "+", 
       RowBox[{"\[Theta]\[Prime]", " ", 
        SuperscriptBox["\[Sigma]", "2"]}]}], ")"}]}], 
    RowBox[{"4", " ", 
     RowBox[{"(", 
      RowBox[{
       RowBox[{"-", "2"}], "+", "\[Sigma]"}], ")"}], " ", "\[Sigma]"}]], "+", 
   
   RowBox[{
    FractionBox["1", 
     RowBox[{"32", " ", 
      RowBox[{"(", 
       RowBox[{
        RowBox[{"-", "3"}], "+", "\[Sigma]"}], ")"}], " ", 
      SuperscriptBox[
       RowBox[{"(", 
        RowBox[{
         RowBox[{"-", "2"}], "+", "\[Sigma]"}], ")"}], "3"], " ", 
      SuperscriptBox["\[Sigma]", "3"], " ", 
      RowBox[{"(", 
       RowBox[{"1", "+", "\[Sigma]"}], ")"}]}]], 
    SuperscriptBox["t0", "2"], " ", 
    RowBox[{"(", 
     RowBox[{
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"]}], "-", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"]}], "+", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"]}], "-", 
      RowBox[{"10", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", "\[Sigma]"}], "+", 
      RowBox[{"20", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", "\[Sigma]"}], "-", 
      RowBox[{"10", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", "\[Sigma]"}], "-", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "+", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "-", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "-", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "+", 
      RowBox[{"5", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "-", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "-", 
      RowBox[{"10", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "+", 
      RowBox[{"5", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "+", 
      RowBox[{"32", " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "-", 
      RowBox[{"16", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "+", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "-", 
      RowBox[{"16", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "-", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "+", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "-", 
      RowBox[{"8", " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "+", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "+", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "-", 
      RowBox[{"32", " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "+", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "-", 
      RowBox[{"3", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "+", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "+", 
      RowBox[{"6", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "-", 
      RowBox[{"3", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "+", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "-", 
      RowBox[{"6", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "-", 
      RowBox[{"6", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "-", 
      RowBox[{"8", " ", 
       SuperscriptBox["\[Sigma]", "5"]}], "-", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Sigma]", "5"]}], "-", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "5"]}], "-", 
      RowBox[{"6", " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "5"]}], "+", 
      RowBox[{"20", " ", 
       SuperscriptBox["\[Sigma]", "6"]}], "+", 
      RowBox[{"2", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Sigma]", "6"]}], "+", 
      RowBox[{"2", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "6"]}], "+", 
      RowBox[{
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "6"]}], "-", 
      RowBox[{"8", " ", 
       SuperscriptBox["\[Sigma]", "7"]}], "+", 
      SuperscriptBox["\[Sigma]", "8"]}], ")"}]}], "+", 
   RowBox[{
    SuperscriptBox["t0", "3"], 
    RowBox[{"(", 
     RowBox[{
      FractionBox[
       RowBox[{
        RowBox[{"(", 
         RowBox[{
          SuperscriptBox["\[Theta]0", "2"], "-", 
          SuperscriptBox["\[Theta]t", "2"]}], ")"}], " ", 
        RowBox[{"(", 
         RowBox[{
          SuperscriptBox[
           RowBox[{"(", 
            RowBox[{"2", "+", "\[Theta]0"}], ")"}], "2"], "-", 
          SuperscriptBox["\[Theta]t", "2"]}], ")"}], 
        RowBox[{"(", 
         RowBox[{
          SuperscriptBox[
           RowBox[{"(", 
            RowBox[{
             RowBox[{"-", "2"}], "+", "\[Theta]0"}], ")"}], "2"], "-", 
          SuperscriptBox["\[Theta]t", "2"]}], ")"}], " ", "\[Theta]\[Prime]", 
        " ", 
        RowBox[{"(", 
         RowBox[{
          RowBox[{"-", "4"}], "+", 
          SuperscriptBox["\[Theta]\[Prime]", "2"]}], ")"}]}], 
       RowBox[{"4096", " ", 
        RowBox[{"(", 
         RowBox[{
          RowBox[{"-", "4"}], "+", "\[Sigma]"}], ")"}], " ", 
        RowBox[{"(", 
         RowBox[{"2", "+", "\[Sigma]"}], ")"}]}]], "-", 
      FractionBox[
       RowBox[{
        RowBox[{"(", 
         RowBox[{
          SuperscriptBox["\[Theta]0", "2"], "-", 
          SuperscriptBox["\[Theta]t", "2"]}], ")"}], " ", 
        RowBox[{"(", 
         RowBox[{
          SuperscriptBox[
           RowBox[{"(", 
            RowBox[{"1", "+", "\[Theta]0"}], ")"}], "2"], "-", 
          SuperscriptBox["\[Theta]t", "2"]}], ")"}], 
        RowBox[{"(", 
         RowBox[{
          SuperscriptBox[
           RowBox[{"(", 
            RowBox[{
             RowBox[{"-", "1"}], "+", "\[Theta]0"}], ")"}], "2"], "-", 
          SuperscriptBox["\[Theta]t", "2"]}], ")"}], " ", "\[Theta]\[Prime]", 
        " ", 
        RowBox[{"(", 
         RowBox[{
          RowBox[{"-", "1"}], "+", 
          SuperscriptBox["\[Theta]\[Prime]", "2"]}], ")"}]}], 
       RowBox[{"96", " ", 
        RowBox[{"(", 
         RowBox[{
          RowBox[{"-", "3"}], "+", "\[Sigma]"}], ")"}], " ", 
        RowBox[{"(", 
         RowBox[{"1", "+", "\[Sigma]"}], ")"}]}]], "+", 
      RowBox[{
       FractionBox[
        RowBox[{
         SuperscriptBox[
          RowBox[{"(", 
           RowBox[{
            SuperscriptBox["\[Theta]0", "2"], "-", 
            SuperscriptBox["\[Theta]t", "2"]}], ")"}], "3"], " ", 
         SuperscriptBox["\[Theta]\[Prime]", "3"]}], 
        RowBox[{"256", " "}]], 
       RowBox[{"(", 
        RowBox[{
         FractionBox["1", 
          SuperscriptBox[
           RowBox[{"(", 
            RowBox[{
             RowBox[{"-", "2"}], "+", "\[Sigma]"}], ")"}], "5"]], "-", 
         FractionBox["1", 
          SuperscriptBox["\[Sigma]", "5"]]}], ")"}]}], "+", 
      RowBox[{
       RowBox[{"(", 
        RowBox[{
         FractionBox["1", 
          SuperscriptBox["\[Sigma]", "3"]], "-", 
         FractionBox["1", 
          SuperscriptBox[
           RowBox[{"(", 
            RowBox[{
             RowBox[{"-", "2"}], "+", "\[Sigma]"}], ")"}], "3"]]}], ")"}], 
       RowBox[{
        FractionBox["1", 
         RowBox[{"1024", " "}]], 
        RowBox[{"(", 
         RowBox[{
          RowBox[{"4", " ", 
           SuperscriptBox[
            RowBox[{"(", 
             RowBox[{
              SuperscriptBox["\[Theta]0", "2"], "-", 
              SuperscriptBox["\[Theta]t", "2"]}], ")"}], "3"], " ", 
           "\[Theta]\[Prime]"}], "-", 
          RowBox[{
           RowBox[{"(", 
            RowBox[{
             RowBox[{"5", " ", 
              SuperscriptBox["\[Theta]0", "6"]}], "+", 
             RowBox[{"8", " ", 
              SuperscriptBox["\[Theta]t", "4"]}], "+", 
             RowBox[{"15", " ", 
              SuperscriptBox["\[Theta]0", "2"], " ", 
              SuperscriptBox["\[Theta]t", "4"]}], "-", 
             RowBox[{"5", " ", 
              SuperscriptBox["\[Theta]t", "6"]}], "-", 
             RowBox[{
              SuperscriptBox["\[Theta]0", "4"], " ", 
              RowBox[{"(", 
               RowBox[{"8", "+", 
                RowBox[{"15", " ", 
                 SuperscriptBox["\[Theta]t", "2"]}]}], ")"}]}]}], ")"}], " ", 
           
           SuperscriptBox["\[Theta]\[Prime]", "3"]}]}], ")"}]}]}], "+", 
      RowBox[{
       RowBox[{"(", 
        RowBox[{
         FractionBox["1", "\[Sigma]"], "-", 
         FractionBox["1", 
          RowBox[{" ", 
           RowBox[{"(", 
            RowBox[{
             RowBox[{"-", "2"}], "+", "\[Sigma]"}], ")"}]}]]}], ")"}], 
       FractionBox["1", "24576"], 
       RowBox[{"(", 
        RowBox[{
         RowBox[{"-", 
          RowBox[{"(", 
           RowBox[{
            SuperscriptBox["\[Theta]0", "2"], "-", 
            SuperscriptBox["\[Theta]t", "2"]}], ")"}]}], "  ", 
         "\[Theta]\[Prime]", " ", 
         RowBox[{"(", 
          RowBox[{"64", "+", 
           RowBox[{"80", " ", 
            SuperscriptBox["\[Theta]\[Prime]", "2"]}], "+", 
           RowBox[{"8", " ", 
            SuperscriptBox["\[Theta]t", "2"], " ", 
            RowBox[{"(", 
             RowBox[{"20", "-", 
              RowBox[{"29", " ", 
               SuperscriptBox["\[Theta]\[Prime]", "2"]}]}], ")"}]}], "+", 
           RowBox[{
            RowBox[{"(", 
             RowBox[{
              SuperscriptBox["\[Theta]0", "4"], "+", 
              SuperscriptBox["\[Theta]t", "4"]}], ")"}], " ", 
            RowBox[{"(", 
             RowBox[{
              RowBox[{"-", "116"}], "+", 
              RowBox[{"125", " ", 
               SuperscriptBox["\[Theta]\[Prime]", "2"]}]}], ")"}]}], "+", 
           RowBox[{
            SuperscriptBox["\[Theta]0", "2"], " ", 
            RowBox[{"(", 
             RowBox[{"160", "-", 
              RowBox[{"232", " ", 
               SuperscriptBox["\[Theta]\[Prime]", "2"]}], "+", 
              RowBox[{
               SuperscriptBox["\[Theta]t", "2"], " ", 
               RowBox[{"(", 
                RowBox[{"232", "-", 
                 RowBox[{"250", " ", 
                  SuperscriptBox["\[Theta]\[Prime]", "2"]}]}], ")"}]}]}], 
             ")"}]}]}], ")"}]}], ")"}]}]}], ")"}]}]}]}]], "Input",
 CellChangeTimes->{{3.901792261796228*^9, 3.901792262152913*^9}},
 CellLabel->"In[1]:=",ExpressionUUID->"51e5da15-cdc7-48c3-964b-75d035f9ff8f"],

Cell[BoxData[{
 RowBox[{"\[Theta]m", ":=", 
  RowBox[{"-", "m"}]}], "\n", 
 RowBox[{"\[Theta]p", ":=", "m"}], "\n", 
 RowBox[{"\[Theta]s", ":=", "0"}], "\n", 
 RowBox[{"t0", ":=", 
  RowBox[{
   RowBox[{"-", "4"}], "*", "a", "*", "\[Alpha]"}]}], "\n", 
 RowBox[{
  RowBox[{"\[Sigma]", " ", ":=", 
   RowBox[{"2", 
    RowBox[{"(", 
     RowBox[{"l", "+", "1"}], ")"}]}]}], " "}], "\[IndentingNewLine]", 
 RowBox[{"extrafacang", ":=", 
  RowBox[{
   RowBox[{"2", "*", "a", "*", 
    RowBox[{"(", 
     RowBox[{"1", "-", "m"}], ")"}], "*", "\[Alpha]"}], "+", 
   RowBox[{
    RowBox[{"(", 
     RowBox[{"\[Alpha]", " ", "a"}], ")"}], "^", 
    "2"}]}]}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"(*", 
   RowBox[{"\[Alpha]", ":=", 
    RowBox[{"Sqrt", "[", 
     RowBox[{
      RowBox[{
       RowBox[{"(", "\[Omega]", ")"}], "^", "2"}], "-", 
      RowBox[{
       RowBox[{"(", "\[Mu]", ")"}], "^", "2"}]}], "]"}]}], "*)"}]}]}], "Input",\

 CellChangeTimes->{{3.901785523349794*^9, 3.9017855335309963`*^9}, {
  3.90178556877418*^9, 3.9017856927724733`*^9}, {3.9017857379992523`*^9, 
  3.901785788404827*^9}, {3.901785838693307*^9, 3.901785838959484*^9}, {
  3.901785877423338*^9, 3.901785888337969*^9}, {3.901785980802637*^9, 
  3.901785987280414*^9}, {3.90178619317754*^9, 3.901786194974011*^9}, {
  3.917434737805415*^9, 3.917434738397002*^9}, {3.917543687160528*^9, 
  3.917543687835238*^9}, {3.938180776023634*^9, 3.938180822792355*^9}, {
  3.938180993009178*^9, 3.938181005406356*^9}, {3.9381810533105927`*^9, 
  3.938181089046699*^9}},
 CellLabel->"In[2]:=",ExpressionUUID->"897eb352-1395-4082-adab-ddfcfde5df8a"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{"Series", "[", 
  RowBox[{
   RowBox[{
    RowBox[{"-", "extrafacang"}], "-", "\[Lambda]", "+", 
    RowBox[{"eqnK0", "[", 
     RowBox[{
     "\[Theta]m", ",", " ", "\[Theta]p", ",", " ", "\[Theta]s", ",", " ", 
      "\[Sigma]", ",", "t0"}], "]"}]}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Alpha]", ",", "0", ",", "2"}], "}"}]}], "]"}]], "Input",
 CellChangeTimes->{{3.904553403842993*^9, 3.904553415381476*^9}, 
   3.91743477759489*^9, {3.917543712184463*^9, 3.917543712600244*^9}, {
   3.938181109234686*^9, 3.9381811966841*^9}, {3.938181251377677*^9, 
   3.938181252083784*^9}},
 CellLabel->"In[14]:=",ExpressionUUID->"a71b394d-ccf4-4db7-95d4-3d03782358ab"],

Cell[BoxData[
 InterpretationBox[
  RowBox[{
   RowBox[{"(", 
    RowBox[{"l", "+", 
     SuperscriptBox["l", "2"], "-", "\[Lambda]"}], ")"}], "+", 
   RowBox[{
    RowBox[{"(", 
     RowBox[{
      RowBox[{"-", 
       SuperscriptBox["a", "2"]}], "+", 
      FractionBox[
       RowBox[{"2", " ", 
        SuperscriptBox["a", "2"], " ", 
        RowBox[{"(", 
         RowBox[{
          RowBox[{"-", "1"}], "+", "l", "+", 
          SuperscriptBox["l", "2"], "+", 
          SuperscriptBox["m", "2"]}], ")"}]}], 
       RowBox[{
        RowBox[{"(", 
         RowBox[{
          RowBox[{"-", "1"}], "+", 
          RowBox[{"2", " ", "l"}]}], ")"}], " ", 
        RowBox[{"(", 
         RowBox[{"3", "+", 
          RowBox[{"2", " ", "l"}]}], ")"}]}]]}], ")"}], " ", 
    SuperscriptBox["\[Alpha]", "2"]}], "+", 
   InterpretationBox[
    SuperscriptBox[
     RowBox[{"O", "[", "\[Alpha]", "]"}], "3"],
    SeriesData[$CellContext`\[Alpha], 0, {}, 0, 3, 1],
    Editable->False]}],
  SeriesData[$CellContext`\[Alpha], 
   0, {$CellContext`l + $CellContext`l^2 - $CellContext`\[Lambda], 
    0, -$CellContext`a^2 + 
    2 $CellContext`a^2 (-1 + 2 $CellContext`l)^(-1) (3 + 
       2 $CellContext`l)^(-1) (-1 + $CellContext`l + $CellContext`l^2 + \
$CellContext`m^2)}, 0, 3, 1],
  Editable->False]], "Output",
 CellChangeTimes->{
  3.904553416383836*^9, 3.9174343581732197`*^9, {3.9174347788835506`*^9, 
   3.917434785036421*^9}, {3.917543705054818*^9, 3.917543713393169*^9}, {
   3.938181118427256*^9, 3.938181144638208*^9}, {3.938181189474228*^9, 
   3.9381811970840673`*^9}, 3.93818125255543*^9, 3.93818128678729*^9},
 CellLabel->"Out[14]=",ExpressionUUID->"289f3318-79fa-4fee-ae20-3b71d9918971"]
}, Open  ]],

Cell[BoxData[
 RowBox[{
  RowBox[{"\[Lambda]", "[", 
   RowBox[{"m_", ",", "l_"}], "]"}], " ", ":=", " ", 
  RowBox[{
   RowBox[{"(", 
    RowBox[{"l", "+", 
     SuperscriptBox["l", "2"]}], ")"}], "+", 
   RowBox[{
    RowBox[{"(", 
     RowBox[{
      RowBox[{"-", "1"}], "+", 
      FractionBox[
       RowBox[{"2", " ", 
        RowBox[{"(", 
         RowBox[{
          RowBox[{"-", "1"}], "+", "l", "+", 
          SuperscriptBox["l", "2"], "+", 
          SuperscriptBox["m", "2"]}], ")"}]}], 
       RowBox[{
        RowBox[{"(", 
         RowBox[{
          RowBox[{"-", "1"}], "+", 
          RowBox[{"2", " ", "l"}]}], ")"}], " ", 
        RowBox[{"(", 
         RowBox[{"3", "+", 
          RowBox[{"2", " ", "l"}]}], ")"}]}]]}], ")"}], 
    SuperscriptBox["a", "2"], " ", 
    SuperscriptBox["\[Alpha]", "2"]}], "+", 
   InterpretationBox[
    SuperscriptBox[
     RowBox[{"O", "[", "\[Alpha]", "]"}], "3"],
    SeriesData[$CellContext`\[Alpha], 0, {}, 0, 3, 1],
    Editable->False]}]}]], "Input",
 CellChangeTimes->{{3.938181263905815*^9, 3.9381813342776213`*^9}, {
  3.938181393510549*^9, 
  3.93818139832541*^9}},ExpressionUUID->"cf66def5-6c3a-466a-9f3d-\
875351881f70"]
}, Open  ]],

Cell[CellGroupData[{

Cell[TextData[StyleBox["Parameters in the Radial Differential Equation and \
Analytic Expression for M\[Omega]:", "Subsubsection"]], "Subsection",
 CellChangeTimes->{{3.8300250722902308`*^9, 3.830025089376346*^9}, {
  3.9017898240592413`*^9, 3.9017898280253077`*^9}, {3.938168914496099*^9, 
  3.9381689865646667`*^9}, {3.938273256380677*^9, 
  3.9382732677674932`*^9}},ExpressionUUID->"6dfc9c28-0cab-4333-8390-\
d08e6e0d4568"],

Cell["\<\
To derive an asymptotic expression for M\[Omega], we reformulate the \
parameters of the problem as follows: z =M\[Omega],  rplus = M(1 + sin(\
\[Delta])) and  rminus = M(1 - sin(\[Delta])), where M represents the mass of \
the black hole and a/M = cos(\[Delta]).\
\>", "Text",
 CellChangeTimes->{{3.938180285401119*^9, 3.938180410927924*^9}, {
   3.938180490794709*^9, 3.93818062945115*^9}, {3.938180758441063*^9, 
   3.938180768332391*^9}, {3.938181466888914*^9, 3.938181469311522*^9}, {
   3.938181507724976*^9, 3.938181513002969*^9}, {3.938194678461656*^9, 
   3.9381946798735332`*^9}, {3.9382022747357073`*^9, 3.938202277023535*^9}, {
   3.9382023999464417`*^9, 3.938202400457551*^9}, {3.938203124414137*^9, 
   3.938203127989369*^9}, {3.93820387134199*^9, 3.938203888512754*^9}, 
   3.9382639966686563`*^9, {3.9382710836217527`*^9, 3.93827108801256*^9}, {
   3.938272971570373*^9, 3.9382731260545607`*^9}, {3.938411858707562*^9, 
   3.938411863774077*^9}, {3.93841198216672*^9, 3.938412023235301*^9}},
 TextJustification->
  0.75,ExpressionUUID->"0e781e5e-e66d-482b-b84e-ee4ed49b4500"],

Cell[BoxData[{
 RowBox[{"\[Alpha]", ":=", 
  RowBox[{"2", "*", 
   RowBox[{"Sqrt", "[", 
    RowBox[{
     RowBox[{"\[Mu]", "^", "2"}], "-", 
     RowBox[{"z", "^", "2"}]}], "]"}]}]}], "\n", 
 RowBox[{"\[Theta]m", ":=", 
  RowBox[{
   RowBox[{"2", "\[ImaginaryI]", "*", 
    RowBox[{
     RowBox[{"(", 
      RowBox[{"1", "-", 
       RowBox[{"Sin", "[", "\[Delta]", "]"}]}], ")"}], "/", 
     RowBox[{"Sin", "[", "\[Delta]", "]"}]}], "*", "z"}], "-", 
   RowBox[{"\[ImaginaryI]", "*", "m", "*", 
    RowBox[{
     RowBox[{"Cos", "[", "\[Delta]", "]"}], "/", 
     RowBox[{"Sin", "[", "\[Delta]", "]"}]}]}]}]}], "\n", 
 RowBox[{"\[Theta]p", ":=", 
  RowBox[{
   RowBox[{"2", "\[ImaginaryI]", "*", 
    RowBox[{
     RowBox[{"(", 
      RowBox[{"1", "+", 
       RowBox[{"Sin", "[", "\[Delta]", "]"}]}], ")"}], "/", 
     RowBox[{"Sin", "[", "\[Delta]", "]"}]}], "*", "z"}], "-", 
   RowBox[{"\[ImaginaryI]", "*", "m", "*", 
    RowBox[{
     RowBox[{"Cos", "[", "\[Delta]", "]"}], "/", 
     RowBox[{"Sin", "[", "\[Delta]", "]"}]}]}]}]}], "\n", 
 RowBox[{"\[Theta]s", ":=", 
  RowBox[{
   RowBox[{"(", 
    RowBox[{"4", "*", 
     RowBox[{"(", 
      RowBox[{
       RowBox[{"\[Mu]", "^", "2"}], "-", 
       RowBox[{"2", "*", 
        RowBox[{"z", "^", "2"}]}]}], ")"}]}], ")"}], "/", 
   RowBox[{"(", "\[Alpha]", ")"}]}]}], "\n", 
 RowBox[{"u0", ":=", 
  RowBox[{"2", "*", "\[Alpha]", "*", 
   RowBox[{"Sin", "[", "\[Delta]", "]"}]}]}], "\n", 
 RowBox[{"extrafac", ":=", 
  RowBox[{
   RowBox[{
    RowBox[{"-", 
     RowBox[{"(", 
      RowBox[{"4", "*", 
       RowBox[{
        RowBox[{"(", 
         RowBox[{
          RowBox[{"\[Mu]", "^", "2"}], "-", 
          RowBox[{"2", "*", 
           RowBox[{"z", "^", "2"}]}]}], ")"}], "^", "2"}]}], ")"}]}], "/", 
    RowBox[{"(", 
     RowBox[{"\[Alpha]", "^", "2"}], ")"}]}], "+", 
   RowBox[{
    RowBox[{"(", 
     RowBox[{"2", "*", 
      RowBox[{"(", 
       RowBox[{
        RowBox[{"\[Mu]", "^", "2"}], "-", 
        RowBox[{"2", "*", 
         RowBox[{"z", "^", "2"}]}]}], ")"}]}], ")"}], "/", "\[Alpha]"}], "+", 
   
   RowBox[{"\[ImaginaryI]", "*", "\[Alpha]", "*", 
    RowBox[{"(", 
     RowBox[{
      RowBox[{"2", "*", "z"}], "-", 
      RowBox[{"m", "*", 
       RowBox[{"Cos", "[", "\[Delta]", "]"}]}], "+", 
      RowBox[{
       RowBox[{"(", 
        RowBox[{"\[ImaginaryI]", "+", 
         RowBox[{"2", "*", "z"}]}], ")"}], "*", 
       RowBox[{"Sin", "[", "\[Delta]", "]"}]}]}], ")"}]}], "+", 
   RowBox[{
    RowBox[{"(", 
     RowBox[{"1", "/", "2"}], ")"}], "*", 
    RowBox[{"(", 
     RowBox[{
      RowBox[{"3", "*", 
       RowBox[{"\[Mu]", "^", "2"}]}], "-", 
      RowBox[{"15", "*", 
       RowBox[{"z", "^", "2"}]}], "-", 
      RowBox[{
       RowBox[{"\[Mu]", "^", "2"}], "*", 
       RowBox[{"Cos", "[", 
        RowBox[{"2", "\[Delta]"}], "]"}]}], "+", 
      RowBox[{
       RowBox[{"z", "^", "2"}], "*", 
       RowBox[{"Cos", "[", 
        RowBox[{"2", "\[Delta]"}], "]"}]}], "+", 
      RowBox[{"4", "*", 
       RowBox[{"\[Mu]", "^", "2"}], "*", 
       RowBox[{"Sin", "[", "\[Delta]", "]"}]}], "-", 
      RowBox[{"8", "*", 
       RowBox[{"z", "^", "2"}], "*", 
       RowBox[{"Sin", "[", "\[Delta]", "]"}]}]}], ")"}]}]}]}]}], "Input",
 CellChangeTimes->{{3.901739119984309*^9, 3.901739503361196*^9}, {
  3.901739564113945*^9, 3.901739566832197*^9}, {3.901783350480748*^9, 
  3.901783351271489*^9}, {3.904049351807171*^9, 3.9040493564131527`*^9}, {
  3.904049446388474*^9, 3.904049447520602*^9}, {3.938183529591524*^9, 
  3.938183567606084*^9}, {3.938183641483345*^9, 3.938183643684037*^9}},
 CellLabel->"In[1]:=",ExpressionUUID->"42249c9b-6279-4a00-8062-dc3cd9b9a8c6"],

Cell[CellGroupData[{

Cell["Perturbative expansions in the limit a/M \[Rule] 1 or \[Delta] \[Rule] \
0:", "Subsubsection",
 CellChangeTimes->{{3.938179530519949*^9, 3.938179596424745*^9}, {
   3.9381796265258503`*^9, 3.938179627715275*^9}, {3.9381839117887583`*^9, 
   3.938183912183035*^9}, {3.938201861136628*^9, 3.938201866675659*^9}, 
   3.938201940033923*^9},ExpressionUUID->"bada998a-328e-4ace-a050-\
878085cac934"],

Cell["\<\
With all parameters established, we examine the perturbative expansion for \
the frequency z, the angular eigenvalue \[Lambda] and the monodromy parameter \
\[Sigma]. As \[Delta] \[Rule] 0, M\[Omega] \[TildeTilde] m/2, as expected. \
\>", "Text",
 CellChangeTimes->{{3.938180285401119*^9, 3.938180410927924*^9}, {
   3.938180490794709*^9, 3.93818062945115*^9}, {3.938180758441063*^9, 
   3.938180768332391*^9}, {3.938181466888914*^9, 3.938181469311522*^9}, {
   3.938181507724976*^9, 3.938181513002969*^9}, {3.938194678461656*^9, 
   3.9381946798735332`*^9}, {3.9382022747357073`*^9, 3.938202277023535*^9}, {
   3.9382023999464417`*^9, 3.938202400457551*^9}, {3.938203124414137*^9, 
   3.938203127989369*^9}, {3.93820387134199*^9, 3.938203888512754*^9}, 
   3.9382639966686563`*^9, {3.9382710836217527`*^9, 3.93827108801256*^9}, {
   3.938272971570373*^9, 3.9382731260545607`*^9}, {3.9382734477045937`*^9, 
   3.938273543225835*^9}, {3.938273633437293*^9, 3.9382736687623672`*^9}, {
   3.9382737150333557`*^9, 3.9382737407244864`*^9}, {3.93841222204696*^9, 
   3.938412228617383*^9}},
 TextJustification->
  0.75,ExpressionUUID->"775fc218-d170-4800-8c69-5f54d93cbbc9"],

Cell[BoxData[
 RowBox[{"z", ":=", " ", 
  RowBox[{
   RowBox[{"m", "/", "2"}], "+", " ", 
   RowBox[{"z1", " ", "\[Delta]"}], "+", 
   RowBox[{"z2", " ", 
    RowBox[{"\[Delta]", "^", "2"}]}]}]}]], "Input",
 CellChangeTimes->{{3.901778907478166*^9, 3.901778959819158*^9}, {
  3.901780811889432*^9, 3.901780816299181*^9}, {3.901784035117194*^9, 
  3.901784039323586*^9}, {3.901797454551095*^9, 3.90179745795323*^9}, {
  3.901799685911182*^9, 3.9017996900773163`*^9}, {3.901800419646635*^9, 
  3.901800427655652*^9}, {3.9018005286879663`*^9, 3.9018005345773277`*^9}, {
  3.9018005668306637`*^9, 3.901800569409313*^9}, {3.901800654382614*^9, 
  3.9018006624740057`*^9}, {3.938183652252145*^9, 3.938183655183557*^9}},
 CellLabel->"In[9]:=",ExpressionUUID->"8daca6b0-7194-424b-a651-dfbde5712bed"],

Cell[BoxData[{
 RowBox[{"\[Lambda]", ":=", 
  RowBox[{"\[Lambda]0", "+", 
   RowBox[{"\[Lambda]1", " ", "\[Delta]"}], "+", 
   RowBox[{"\[Lambda]2", " ", 
    SuperscriptBox["\[Delta]", "2"]}]}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Sigma]", ":=", " ", 
  RowBox[{"1", "+", "\[Sigma]0", "+", 
   RowBox[{"\[Sigma]1", " ", "\[Delta]"}], "+", 
   RowBox[{"\[Sigma]2", " ", 
    RowBox[{"\[Delta]", "^", "2"}]}]}]}]}], "Input",
 CellChangeTimes->{{3.825103951022374*^9, 3.825103962182341*^9}, 
   3.825105127532143*^9, {3.825107130104211*^9, 3.825107169971271*^9}, {
   3.825107332287709*^9, 3.825107334376617*^9}, 3.829779831652659*^9, {
   3.829779925873712*^9, 3.8297799303824253`*^9}, {3.829780162021459*^9, 
   3.829780177007147*^9}, {3.829780390272717*^9, 3.82978040090659*^9}, {
   3.829780462393663*^9, 3.8297804642749662`*^9}, 3.829780503645982*^9, {
   3.82978054140519*^9, 3.8297805423994007`*^9}, {3.829836012966839*^9, 
   3.829836078467174*^9}, {3.8298362666714973`*^9, 3.829836274134432*^9}, {
   3.8298471258263817`*^9, 3.8298471287927513`*^9}, {3.901782378626204*^9, 
   3.901782392821439*^9}, {3.901782474471959*^9, 3.901782475455517*^9}, {
   3.901783550730558*^9, 3.9017835532688713`*^9}, {3.901784193560364*^9, 
   3.901784200027437*^9}, {3.901797481630755*^9, 3.901797495093293*^9}, {
   3.901799597362521*^9, 3.901799607283207*^9}, {3.9017997797952843`*^9, 
   3.901799801068417*^9}, 3.90179983963925*^9, {3.901800542161705*^9, 
   3.901800576524435*^9}, {3.901800663773429*^9, 3.901800671558414*^9}, {
   3.938183657264267*^9, 3.93818366480238*^9}},
 CellLabel->"In[8]:=",ExpressionUUID->"cfa24386-7085-45ce-8ebf-7fbdaa0c2b2e"]
}, Open  ]],

Cell[CellGroupData[{

Cell["\<\
Using the accessory parameter for small t0, we can compute the first terms\
\>", "Subsubsection",
 CellChangeTimes->{{3.938168995803297*^9, 3.938169018669443*^9}, {
  3.938201972002821*^9, 3.9382019729037113`*^9}, {3.938418490394504*^9, 
  3.9384185423909817`*^9}},ExpressionUUID->"7faf3c8c-c0bf-49a7-a918-\
bcdc114f28c5"],

Cell[BoxData[
 RowBox[{
  RowBox[{"eqnK0", "[", 
   RowBox[{
   "\[Theta]0_", ",", " ", "\[Theta]t_", ",", " ", "\[Theta]\[Prime]_", ",", 
    " ", "\[Sigma]_", ",", "t0_"}], "]"}], ":=", 
  RowBox[{
   RowBox[{
    RowBox[{"-", 
     FractionBox["1", "4"]}], " ", 
    RowBox[{"(", 
     RowBox[{"\[Theta]\[Prime]", "-", "\[Sigma]"}], ")"}], " ", 
    RowBox[{"(", 
     RowBox[{
      RowBox[{"-", "2"}], "+", "\[Theta]\[Prime]", "+", "\[Sigma]"}], ")"}]}],
    "+", 
   FractionBox[
    RowBox[{"t0", 
     RowBox[{"(", 
      RowBox[{
       RowBox[{
        RowBox[{"-", 
         SuperscriptBox["\[Theta]0", "2"]}], " ", "\[Theta]\[Prime]"}], "+", 
       RowBox[{
        SuperscriptBox["\[Theta]t", "2"], " ", "\[Theta]\[Prime]"}], "+", 
       RowBox[{"4", " ", "\[Sigma]"}], "-", 
       RowBox[{"4", " ", "\[Theta]t", " ", "\[Sigma]"}], "-", 
       RowBox[{"2", " ", "\[Theta]\[Prime]", " ", "\[Sigma]"}], "-", 
       RowBox[{"2", " ", 
        SuperscriptBox["\[Sigma]", "2"]}], "+", 
       RowBox[{"2", " ", "\[Theta]t", " ", 
        SuperscriptBox["\[Sigma]", "2"]}], "+", 
       RowBox[{"\[Theta]\[Prime]", " ", 
        SuperscriptBox["\[Sigma]", "2"]}]}], ")"}]}], 
    RowBox[{"4", " ", 
     RowBox[{"(", 
      RowBox[{
       RowBox[{"-", "2"}], "+", "\[Sigma]"}], ")"}], " ", "\[Sigma]"}]], "+", 
   
   RowBox[{
    FractionBox["1", 
     RowBox[{"32", " ", 
      RowBox[{"(", 
       RowBox[{
        RowBox[{"-", "3"}], "+", "\[Sigma]"}], ")"}], " ", 
      SuperscriptBox[
       RowBox[{"(", 
        RowBox[{
         RowBox[{"-", "2"}], "+", "\[Sigma]"}], ")"}], "3"], " ", 
      SuperscriptBox["\[Sigma]", "3"], " ", 
      RowBox[{"(", 
       RowBox[{"1", "+", "\[Sigma]"}], ")"}]}]], 
    SuperscriptBox["t0", "2"], " ", 
    RowBox[{"(", 
     RowBox[{
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"]}], "-", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"]}], "+", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"]}], "-", 
      RowBox[{"10", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", "\[Sigma]"}], "+", 
      RowBox[{"20", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", "\[Sigma]"}], "-", 
      RowBox[{"10", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", "\[Sigma]"}], "-", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "+", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "-", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "-", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "+", 
      RowBox[{"5", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "-", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "-", 
      RowBox[{"10", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "+", 
      RowBox[{"5", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "2"]}], "+", 
      RowBox[{"32", " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "-", 
      RowBox[{"16", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "+", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "-", 
      RowBox[{"16", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "-", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "+", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "-", 
      RowBox[{"8", " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "+", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "+", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "3"]}], "-", 
      RowBox[{"32", " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "+", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "-", 
      RowBox[{"3", " ", 
       SuperscriptBox["\[Theta]0", "4"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "+", 
      RowBox[{"24", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "+", 
      RowBox[{"6", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "-", 
      RowBox[{"3", " ", 
       SuperscriptBox["\[Theta]t", "4"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "+", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "-", 
      RowBox[{"6", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "-", 
      RowBox[{"6", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "4"]}], "-", 
      RowBox[{"8", " ", 
       SuperscriptBox["\[Sigma]", "5"]}], "-", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Sigma]", "5"]}], "-", 
      RowBox[{"12", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "5"]}], "-", 
      RowBox[{"6", " ", 
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "5"]}], "+", 
      RowBox[{"20", " ", 
       SuperscriptBox["\[Sigma]", "6"]}], "+", 
      RowBox[{"2", " ", 
       SuperscriptBox["\[Theta]0", "2"], " ", 
       SuperscriptBox["\[Sigma]", "6"]}], "+", 
      RowBox[{"2", " ", 
       SuperscriptBox["\[Theta]t", "2"], " ", 
       SuperscriptBox["\[Sigma]", "6"]}], "+", 
      RowBox[{
       SuperscriptBox["\[Theta]\[Prime]", "2"], " ", 
       SuperscriptBox["\[Sigma]", "6"]}], "-", 
      RowBox[{"8", " ", 
       SuperscriptBox["\[Sigma]", "7"]}], "+", 
      SuperscriptBox["\[Sigma]", "8"]}], ")"}]}]}]}]], "Input",
 CellChangeTimes->{{3.825101861135518*^9, 3.825101911700745*^9}, {
   3.825103208444289*^9, 3.8251032120863733`*^9}, {3.8251034969269743`*^9, 
   3.825103515915007*^9}, 3.82978003169919*^9, {3.829781885423608*^9, 
   3.829781885960772*^9}, {3.829815340969594*^9, 3.829815342110276*^9}, {
   3.829819284762578*^9, 3.829819302055241*^9}, {3.829819337727669*^9, 
   3.829819362534358*^9}, {3.829822775866643*^9, 3.8298227766930523`*^9}, 
   3.829822858158559*^9, 3.829825089796706*^9, {3.829843491924644*^9, 
   3.8298435063859987`*^9}, {3.901784083168501*^9, 3.9017840934100246`*^9}, {
   3.901785498352248*^9, 3.9017855140387297`*^9}},
 CellLabel->"In[10]:=",ExpressionUUID->"a4802582-eafa-410b-99cf-cae768b02ffc"],

Cell[BoxData[
 RowBox[{
  RowBox[{
   RowBox[{"Series", "[", " ", 
    RowBox[{
     RowBox[{
      RowBox[{"(", 
       RowBox[{
        RowBox[{"-", "3"}], "+", "\[Sigma]"}], ")"}], " ", 
      SuperscriptBox[
       RowBox[{"(", 
        RowBox[{
         RowBox[{"-", "2"}], "+", "\[Sigma]"}], ")"}], "3"], " ", 
      SuperscriptBox["\[Sigma]", "3"], " ", 
      RowBox[{"(", 
       RowBox[{"1", "+", "\[Sigma]"}], ")"}], 
      RowBox[{"(", " ", 
       RowBox[{
        RowBox[{"-", "extrafac"}], "-", "\[Lambda]", "+", 
        RowBox[{"eqnK0", "[", 
         RowBox[{
         "\[Theta]m", ",", " ", "\[Theta]p", ",", " ", "\[Theta]s", ",", " ", 
          "\[Sigma]", ",", "u0"}], "]"}]}], ")"}]}], ",", 
     RowBox[{"{", 
      RowBox[{"\[Delta]", ",", "0", ",", "1"}], "}"}]}], "]"}], "//", 
   "Simplify"}], ";"}]], "Input",
 CellChangeTimes->{{3.825101916843148*^9, 3.825101967455206*^9}, {
   3.825102050115437*^9, 3.8251020733478107`*^9}, {3.82510212356104*^9, 
   3.825102177434291*^9}, {3.825102271039723*^9, 3.825102281445594*^9}, {
   3.825102555730928*^9, 3.825102558819799*^9}, {3.825103289320608*^9, 
   3.825103289490994*^9}, {3.825103441838015*^9, 3.825103449860764*^9}, {
   3.825105198472012*^9, 3.825105210014556*^9}, {3.829780103362804*^9, 
   3.829780153022387*^9}, {3.829780209914707*^9, 3.829780210656454*^9}, {
   3.829781894224218*^9, 3.829781901537375*^9}, {3.8298146930114326`*^9, 
   3.8298146938208227`*^9}, {3.829814730489767*^9, 3.829814730988206*^9}, {
   3.829823025862752*^9, 3.829823110016797*^9}, {3.829825140252026*^9, 
   3.829825141378757*^9}, {3.829825490992483*^9, 3.8298254993492107`*^9}, 
   3.829836086502471*^9, 3.829836341061569*^9, {3.82984352301235*^9, 
   3.829843536776372*^9}, {3.9017835708441277`*^9, 3.901783575860958*^9}, 
   3.901783614114406*^9, {3.901783670550692*^9, 3.901783672192411*^9}, {
   3.901784107341888*^9, 3.901784150396459*^9}, {3.901797586471672*^9, 
   3.901797617812235*^9}, {3.9017996167853193`*^9, 3.901799617228814*^9}, {
   3.901800592395115*^9, 3.9018005959701557`*^9}, {3.90180069599621*^9, 
   3.901800700993754*^9}, {3.938183669338097*^9, 3.9381836695286503`*^9}, 
   3.938412269447667*^9},
 CellLabel->"In[13]:=",ExpressionUUID->"8b04c223-3423-470a-9a8b-e05cf160d71c"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{
  RowBox[{"Solve", "[", 
   RowBox[{
    RowBox[{
     RowBox[{
      FractionBox["1", "4"], " ", 
      RowBox[{"(", 
       RowBox[{
        RowBox[{"-", "2"}], "+", "\[Sigma]0"}], ")"}], " ", 
      RowBox[{"(", 
       RowBox[{"2", "+", "\[Sigma]0"}], ")"}], " ", 
      SuperscriptBox[
       RowBox[{"(", 
        RowBox[{
         RowBox[{"-", "1"}], "+", 
         SuperscriptBox["\[Sigma]0", "2"]}], ")"}], "3"], " ", 
      RowBox[{"(", 
       RowBox[{
        RowBox[{"-", "1"}], "+", 
        RowBox[{"7", " ", 
         SuperscriptBox["m", "2"]}], "-", 
        RowBox[{"4", " ", "\[Lambda]0"}], "-", 
        RowBox[{"4", " ", 
         SuperscriptBox["\[Mu]", "2"]}], "+", 
        SuperscriptBox["\[Sigma]0", "2"]}], ")"}]}], "\[Equal]", "0"}], ",", 
    " ", "\[Sigma]0"}], "]"}], " ", "//", "FullSimplify"}]], "Input",
 CellChangeTimes->{{3.829780233530409*^9, 3.829780248897859*^9}, {
   3.829780528617422*^9, 3.829780531395029*^9}, {3.829780578039548*^9, 
   3.829780578538744*^9}, {3.829781915980891*^9, 3.829781916519994*^9}, {
   3.829814743313723*^9, 3.8298147438447943`*^9}, {3.829823127370953*^9, 
   3.8298231279031963`*^9}, {3.829825530554957*^9, 3.829825531084631*^9}, {
   3.829836111422381*^9, 3.829836111971093*^9}, 3.8298363239258347`*^9, 
   3.8298364006371613`*^9, {3.829843563729116*^9, 3.8298435643134937`*^9}, 
   3.9017976572066183`*^9, {3.901797715707185*^9, 3.901797717299345*^9}, 
   3.9017978048295507`*^9, 3.901799634863021*^9, {3.901799718157934*^9, 
   3.901799754088666*^9}, 3.901799824007959*^9, 3.901799912562984*^9, {
   3.901800478310577*^9, 3.90180048278389*^9}, 3.9018006165763083`*^9, 
   3.904205444122661*^9, 3.938183931246353*^9},
 CellLabel->"In[12]:=",ExpressionUUID->"1a1f763e-d709-4ca2-9266-2ce530b6bc14"],

Cell[BoxData[
 RowBox[{"{", 
  RowBox[{
   RowBox[{"{", 
    RowBox[{"\[Sigma]0", "\[Rule]", 
     RowBox[{"-", "2"}]}], "}"}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Sigma]0", "\[Rule]", 
     RowBox[{"-", "1"}]}], "}"}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Sigma]0", "\[Rule]", 
     RowBox[{"-", "1"}]}], "}"}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Sigma]0", "\[Rule]", 
     RowBox[{"-", "1"}]}], "}"}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Sigma]0", "\[Rule]", "1"}], "}"}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Sigma]0", "\[Rule]", "1"}], "}"}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Sigma]0", "\[Rule]", "1"}], "}"}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Sigma]0", "\[Rule]", "2"}], "}"}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Sigma]0", "\[Rule]", 
     RowBox[{"-", 
      SqrtBox[
       RowBox[{"1", "-", 
        RowBox[{"7", " ", 
         SuperscriptBox["m", "2"]}], "+", 
        RowBox[{"4", " ", "\[Lambda]0"}], "+", 
        RowBox[{"4", " ", 
         SuperscriptBox["\[Mu]", "2"]}]}]]}]}], "}"}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Sigma]0", "\[Rule]", 
     SqrtBox[
      RowBox[{"1", "-", 
       RowBox[{"7", " ", 
        SuperscriptBox["m", "2"]}], "+", 
       RowBox[{"4", " ", "\[Lambda]0"}], "+", 
       RowBox[{"4", " ", 
        SuperscriptBox["\[Mu]", "2"]}]}]]}], "}"}]}], "}"}]], "Output",
 CellChangeTimes->{
  3.901799636541383*^9, {3.9017997201349983`*^9, 3.901799754503632*^9}, 
   3.901799824782621*^9, 3.901799913447836*^9, 3.901800483444715*^9, 
   3.9018006513087606`*^9, 3.904205444779418*^9, 3.9174326117007113`*^9, 
   3.9181171976867228`*^9, 3.938183932037656*^9},
 CellLabel->"Out[12]=",ExpressionUUID->"345147c4-2773-438d-9383-187fb6653ea9"]
}, Open  ]]
}, Open  ]],

Cell[CellGroupData[{

Cell[TextData[{
 "Taking the negative solution for \[Sigma]0, one has the following \
asymptotic expression ",
 Cell[BoxData[
  RowBox[{"\[Sigma]", ":=", " ", 
   RowBox[{"1", "-", 
    SqrtBox[
     RowBox[{"1", "-", 
      RowBox[{"7", " ", 
       SuperscriptBox["m", "2"]}], "+", 
      RowBox[{"4", " ", "\[Lambda]0"}], "+", 
      RowBox[{"4", " ", 
       SuperscriptBox["\[Mu]", "2"]}]}]], " ", "+", " ", 
    RowBox[{"O", "[", "\[Delta]", "]"}]}]}]],
  CellChangeTimes->{{3.8298467513163223`*^9, 3.829846752585395*^9}, 
    3.829853579249111*^9, 3.829905434602426*^9, 3.8299058625474167`*^9, 
    3.8299075564844637`*^9, {3.829911092704769*^9, 3.8299111197743473`*^9}, {
    3.8299134945088997`*^9, 3.829913495044894*^9}, 3.829913529234529*^9, 
    3.829913591925922*^9, {3.829913692987479*^9, 3.829913693559906*^9}, {
    3.8299307035929213`*^9, 3.829930728493832*^9}, 3.829994239800318*^9, 
    3.830003741267404*^9, 3.830006138185238*^9, 3.8300074135406427`*^9, 
    3.830008193711309*^9, 3.83000907117076*^9, 3.830009523989856*^9, 
    3.830010295333642*^9, 3.8300194968373947`*^9, {3.90180077646868*^9, 
    3.9018007983404217`*^9}, {3.9018008517871943`*^9, 3.9018009660570927`*^9},
     3.901801273252368*^9, {3.901801306951016*^9, 3.901801307548423*^9}, {
    3.918118124889427*^9, 3.918118128808403*^9}, {3.938412407330574*^9, 
    3.938412407751418*^9}, {3.938412534225823*^9, 3.9384125706684732`*^9}, {
    3.938413613296962*^9, 3.9384136234825897`*^9}, {3.938414032964843*^9, 
    3.938414033077137*^9}},ExpressionUUID->
  "de8fdb93-19c4-498c-a477-0a01d74b19f0"]
}], "Subsubsection",
 CellChangeTimes->{{3.830025201194607*^9, 3.830025236300308*^9}, 
   3.901800785287122*^9, {3.901861990538026*^9, 3.9018619986765327`*^9}, {
   3.938179781266294*^9, 3.938179807924893*^9}, {3.938412463552657*^9, 
   3.938412528099616*^9}, {3.9384138059247*^9, 3.938413812999014*^9}, {
   3.9384140435816402`*^9, 3.93841404446742*^9}, {3.938414189529913*^9, 
   3.938414215636841*^9}},ExpressionUUID->"bc039293-5f6e-476d-adc3-\
19c387be8b25"],

Cell[BoxData[
 RowBox[{"\[Sigma]", ":=", " ", 
  RowBox[{"1", "-", 
   SqrtBox[
    RowBox[{"1", "-", 
     RowBox[{"7", " ", 
      SuperscriptBox["m", "2"]}], "+", 
     RowBox[{"4", " ", "\[Lambda]0"}], "+", 
     RowBox[{"4", " ", 
      SuperscriptBox["\[Mu]", "2"]}]}]]}]}]], "Input",
 CellChangeTimes->{{3.8298467513163223`*^9, 3.829846752585395*^9}, 
   3.829853579249111*^9, 3.829905434602426*^9, 3.8299058625474167`*^9, 
   3.8299075564844637`*^9, {3.829911092704769*^9, 3.8299111197743473`*^9}, {
   3.8299134945088997`*^9, 3.829913495044894*^9}, 3.829913529234529*^9, 
   3.829913591925922*^9, {3.829913692987479*^9, 3.829913693559906*^9}, {
   3.8299307035929213`*^9, 3.829930728493832*^9}, 3.829994239800318*^9, 
   3.830003741267404*^9, 3.830006138185238*^9, 3.8300074135406427`*^9, 
   3.830008193711309*^9, 3.83000907117076*^9, 3.830009523989856*^9, 
   3.830010295333642*^9, 3.8300194968373947`*^9, {3.90180077646868*^9, 
   3.9018007983404217`*^9}, {3.9018008517871943`*^9, 3.9018009660570927`*^9}, 
   3.901801273252368*^9, {3.901801306951016*^9, 3.901801307548423*^9}, {
   3.918118124889427*^9, 3.918118128808403*^9}, {3.938412407330574*^9, 
   3.938412407751418*^9}, {3.938412534225823*^9, 3.9384125706684732`*^9}, {
   3.938413613296962*^9, 3.9384136234825897`*^9}, {3.938414032964843*^9, 
   3.938414048901773*^9}},
 CellLabel->"In[7]:=",ExpressionUUID->"169d079e-1cb6-41e9-b6d1-46390addb01a"]
}, Open  ]],

Cell[CellGroupData[{

Cell[TextData[StyleBox["Derivation of the first terms for the expansion of M\
\[Omega] or z : ", "Subsubsection"]], "Subsubsection",
 CellChangeTimes->{{3.938180285401119*^9, 3.938180410927924*^9}, {
   3.938180490794709*^9, 3.93818062945115*^9}, {3.938180758441063*^9, 
   3.938180768332391*^9}, {3.938181466888914*^9, 3.938181469311522*^9}, {
   3.938181507724976*^9, 3.938181513002969*^9}, {3.938194678461656*^9, 
   3.9381946798735332`*^9}, {3.9382022747357073`*^9, 3.938202277023535*^9}, {
   3.9382023999464417`*^9, 3.938202400457551*^9}, {3.938203124414137*^9, 
   3.938203127989369*^9}, {3.93820387134199*^9, 3.938203888512754*^9}, 
   3.9382639966686563`*^9, {3.9382710836217527`*^9, 3.93827108801256*^9}, {
   3.938272971570373*^9, 3.9382731260545607`*^9}, {3.9382734477045937`*^9, 
   3.938273543225835*^9}, {3.938273633437293*^9, 3.9382736687623672`*^9}, {
   3.9382737150333557`*^9, 3.9382737407244864`*^9}, {3.93841222204696*^9, 
   3.938412228617383*^9}, {3.93841244478471*^9, 3.938412457752995*^9}, {
   3.938443212774149*^9, 3.938443231905076*^9}},
 TextJustification->
  0.75,ExpressionUUID->"52fbabbe-fcac-4fbe-a9af-2c0ac8bc9648"],

Cell[BoxData[{
 RowBox[{"\[Alpha]", ":=", 
  RowBox[{"2", "*", 
   RowBox[{"Sqrt", "[", 
    RowBox[{
     RowBox[{"\[Mu]", "^", "2"}], "-", 
     RowBox[{"z", "^", "2"}]}], "]"}]}]}], "\n", 
 RowBox[{"\[Theta]m", ":=", 
  RowBox[{
   RowBox[{"2", "\[ImaginaryI]", "*", 
    RowBox[{
     RowBox[{"(", 
      RowBox[{"1", "-", 
       RowBox[{"Sin", "[", "\[Delta]", "]"}]}], ")"}], "/", 
     RowBox[{"Sin", "[", "\[Delta]", "]"}]}], "*", "z"}], "-", 
   RowBox[{"\[ImaginaryI]", "*", "m", "*", 
    RowBox[{
     RowBox[{"Cos", "[", "\[Delta]", "]"}], "/", 
     RowBox[{"Sin", "[", "\[Delta]", "]"}]}]}]}]}], "\n", 
 RowBox[{"\[Theta]p", ":=", 
  RowBox[{
   RowBox[{"2", "\[ImaginaryI]", "*", 
    RowBox[{
     RowBox[{"(", 
      RowBox[{"1", "+", 
       RowBox[{"Sin", "[", "\[Delta]", "]"}]}], ")"}], "/", 
     RowBox[{"Sin", "[", "\[Delta]", "]"}]}], "*", "z"}], "-", 
   RowBox[{"\[ImaginaryI]", "*", "m", "*", 
    RowBox[{
     RowBox[{"Cos", "[", "\[Delta]", "]"}], "/", 
     RowBox[{"Sin", "[", "\[Delta]", "]"}]}]}]}]}], "\n", 
 RowBox[{"\[Theta]s", ":=", 
  RowBox[{
   RowBox[{"(", 
    RowBox[{"4", "*", 
     RowBox[{"(", 
      RowBox[{
       RowBox[{"\[Mu]", "^", "2"}], "-", 
       RowBox[{"2", "*", 
        RowBox[{"z", "^", "2"}]}]}], ")"}]}], ")"}], "/", 
   RowBox[{"(", "\[Alpha]", ")"}]}]}], "\n", 
 RowBox[{"u0", ":=", 
  RowBox[{"2", "*", "\[Alpha]", "*", 
   RowBox[{"Sin", "[", "\[Delta]", "]"}]}]}], "\[IndentingNewLine]", 
 RowBox[{"z", ":=", " ", 
  RowBox[{
   RowBox[{"m", "/", "2"}], "+", " ", 
   RowBox[{"z1", " ", "\[Delta]"}], "+", 
   RowBox[{"z2", " ", 
    RowBox[{"\[Delta]", "^", "2"}]}]}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Sigma]", ":=", 
  RowBox[{"1", " ", "-", " ", "\[Sigma]0"}]}]}], "Input",
 CellChangeTimes->{{3.938418187191494*^9, 3.938418204829088*^9}},
 CellLabel->"In[2]:=",ExpressionUUID->"bf8d7159-fe84-44a4-a13c-edd220e1c70c"]
}, Open  ]],

Cell[CellGroupData[{

Cell["Equation (16) in 2408.13964:", "Subsubsection",
 CellChangeTimes->{{3.9384374140128527`*^9, 3.938437440951729*^9}, {
   3.938437666171742*^9, 3.9384376669289837`*^9}, {3.*************^9, 
   3.9384377049000072`*^9}, 3.9384378139493847`*^9, {3.938437906744025*^9, 
   3.938437915775786*^9}},ExpressionUUID->"be025626-ed0a-4b3f-8e64-\
3b4b7fc69e68"],

Cell[BoxData[
 RowBox[{
  RowBox[{"ess", "[", 
   RowBox[{"\[Theta]p_", ",", "\[Theta]m_", ",", "\[Sigma]_"}], "]"}], ":=", 
  " ", 
  RowBox[{
   RowBox[{"Exp", "[", 
    RowBox[{
     RowBox[{"-", "\[ImaginaryI]"}], " ", "\[Pi]", " ", "\[Sigma]"}], "]"}], 
   RowBox[{"(", 
    RowBox[{
     RowBox[{"(", 
      RowBox[{
       RowBox[{"Sin", "[", 
        RowBox[{
         FractionBox["\[Pi]", "2"], 
         RowBox[{"(", 
          RowBox[{"\[Theta]s", "+", "\[Sigma]"}], ")"}]}], "]"}], 
       RowBox[{"Sin", "[", 
        RowBox[{
         FractionBox["\[Pi]", "2"], 
         RowBox[{"(", 
          RowBox[{"\[Theta]m", "+", "\[Theta]p", "+", "\[Sigma]"}], ")"}]}], 
        "]"}], 
       RowBox[{"Sin", "[", 
        RowBox[{
         FractionBox["\[Pi]", "2"], 
         RowBox[{"(", 
          RowBox[{
           RowBox[{"-", "\[Theta]m"}], "+", "\[Theta]p", "+", "\[Sigma]"}], 
          ")"}]}], "]"}]}], ")"}], "/", 
     RowBox[{"(", 
      RowBox[{
       RowBox[{"Sin", "[", 
        RowBox[{
         FractionBox["\[Pi]", "2"], 
         RowBox[{"(", 
          RowBox[{"\[Theta]s", "-", "\[Sigma]"}], ")"}]}], "]"}], 
       RowBox[{"Sin", "[", 
        RowBox[{
         FractionBox["\[Pi]", "2"], 
         RowBox[{"(", 
          RowBox[{"\[Theta]m", "+", "\[Theta]p", "-", "\[Sigma]"}], ")"}]}], 
        "]"}], 
       RowBox[{"Sin", "[", 
        RowBox[{
         FractionBox["\[Pi]", "2"], 
         RowBox[{"(", 
          RowBox[{
           RowBox[{"-", "\[Theta]m"}], "+", "\[Theta]p", "-", "\[Sigma]"}], 
          ")"}]}], "]"}]}], ")"}]}], ")"}]}]}]], "Input",
 CellChangeTimes->{{3.938418224098137*^9, 3.9384182263534803`*^9}},
 CellLabel->"In[1]:=",ExpressionUUID->"42ffa095-96aa-4490-a111-0d2205b1c555"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{"Series", "[", 
  RowBox[{
   RowBox[{"ess", "[", 
    RowBox[{"\[Theta]p", ",", "\[Theta]m", ",", "\[Sigma]"}], "]"}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Delta]", ",", "0", ",", "0"}], "}"}]}], "]"}]], "Input",
 CellChangeTimes->{{3.938443879026238*^9, 3.938443891667017*^9}},
 CellLabel->"In[9]:=",ExpressionUUID->"ee27f6b3-cf07-4fb7-8581-1fa861027255"],

Cell[BoxData[
 InterpretationBox[
  RowBox[{
   RowBox[{
    SuperscriptBox["\[ExponentialE]", 
     RowBox[{
      RowBox[{"-", "\[ImaginaryI]"}], " ", "\[Pi]", " ", 
      RowBox[{"(", 
       RowBox[{"1", "-", "\[Sigma]0"}], ")"}]}]], " ", 
    RowBox[{"Csc", "[", 
     RowBox[{
      FractionBox["1", "2"], " ", "\[Pi]", " ", 
      RowBox[{"(", 
       RowBox[{
        RowBox[{"-", "1"}], "+", 
        RowBox[{"2", " ", "\[ImaginaryI]", " ", "m"}], "+", "\[Sigma]0"}], 
       ")"}]}], "]"}], " ", 
    RowBox[{"Csc", "[", 
     RowBox[{
      FractionBox["1", "2"], " ", "\[Pi]", " ", 
      RowBox[{"(", 
       RowBox[{
        RowBox[{"-", "1"}], "+", 
        RowBox[{"4", " ", "\[ImaginaryI]", " ", "z1"}], "+", "\[Sigma]0"}], 
       ")"}]}], "]"}], " ", 
    RowBox[{"Csc", "[", 
     RowBox[{
      FractionBox["1", "2"], " ", "\[Pi]", " ", 
      RowBox[{"(", 
       RowBox[{
        RowBox[{"-", "1"}], "-", 
        FractionBox[
         RowBox[{"2", " ", 
          RowBox[{"(", 
           RowBox[{
            SuperscriptBox["m", "2"], "-", 
            RowBox[{"2", " ", 
             SuperscriptBox["\[Mu]", "2"]}]}], ")"}]}], 
         SqrtBox[
          RowBox[{
           RowBox[{"-", 
            SuperscriptBox["m", "2"]}], "+", 
           RowBox[{"4", " ", 
            SuperscriptBox["\[Mu]", "2"]}]}]]], "+", "\[Sigma]0"}], ")"}]}], 
     "]"}], " ", 
    RowBox[{"Sin", "[", 
     RowBox[{
      FractionBox["1", "2"], " ", "\[Pi]", " ", 
      RowBox[{"(", 
       RowBox[{"1", "+", 
        RowBox[{"2", " ", "\[ImaginaryI]", " ", "m"}], "-", "\[Sigma]0"}], 
       ")"}]}], "]"}], " ", 
    RowBox[{"Sin", "[", 
     RowBox[{
      FractionBox["1", "2"], " ", "\[Pi]", " ", 
      RowBox[{"(", 
       RowBox[{"1", "+", 
        RowBox[{"4", " ", "\[ImaginaryI]", " ", "z1"}], "-", "\[Sigma]0"}], 
       ")"}]}], "]"}], " ", 
    RowBox[{"Sin", "[", 
     RowBox[{
      FractionBox["1", "2"], " ", "\[Pi]", " ", 
      RowBox[{"(", 
       RowBox[{"1", "-", 
        FractionBox[
         RowBox[{"2", " ", 
          RowBox[{"(", 
           RowBox[{
            SuperscriptBox["m", "2"], "-", 
            RowBox[{"2", " ", 
             SuperscriptBox["\[Mu]", "2"]}]}], ")"}]}], 
         SqrtBox[
          RowBox[{
           RowBox[{"-", 
            SuperscriptBox["m", "2"]}], "+", 
           RowBox[{"4", " ", 
            SuperscriptBox["\[Mu]", "2"]}]}]]], "-", "\[Sigma]0"}], ")"}]}], 
     "]"}]}], "+", 
   InterpretationBox[
    SuperscriptBox[
     RowBox[{"O", "[", "\[Delta]", "]"}], "1"],
    SeriesData[$CellContext`\[Delta], 0, {}, 0, 1, 1],
    Editable->False]}],
  SeriesData[$CellContext`\[Delta], 0, {
   E^(Complex[0, -1] Pi (1 - $CellContext`\[Sigma]0)) 
    Csc[Rational[1, 2] 
      Pi (-1 + Complex[0, 2] $CellContext`m + $CellContext`\[Sigma]0)] 
    Csc[Rational[1, 2] 
      Pi (-1 + Complex[0, 4] $CellContext`z1 + $CellContext`\[Sigma]0)] 
    Csc[Rational[1, 2] 
      Pi (-1 - 2 ($CellContext`m^2 - 
        2 $CellContext`\[Mu]^2) (-$CellContext`m^2 + 4 $CellContext`\[Mu]^2)^
        Rational[-1, 2] + $CellContext`\[Sigma]0)] 
    Sin[Rational[1, 2] 
      Pi (1 + Complex[0, 2] $CellContext`m - $CellContext`\[Sigma]0)] 
    Sin[Rational[1, 2] 
      Pi (1 + Complex[0, 4] $CellContext`z1 - $CellContext`\[Sigma]0)] 
    Sin[Rational[1, 2] 
      Pi (1 - 2 ($CellContext`m^2 - 
        2 $CellContext`\[Mu]^2) (-$CellContext`m^2 + 4 $CellContext`\[Mu]^2)^
        Rational[-1, 2] - $CellContext`\[Sigma]0)]}, 0, 1, 1],
  Editable->False]], "Output",
 CellChangeTimes->{3.9384439238246193`*^9},
 CellLabel->"Out[9]=",ExpressionUUID->"473b99a2-6b6d-4176-a234-e42568c67326"]
}, Open  ]]
}, Open  ]],

Cell[CellGroupData[{

Cell["Equation (20) in 2408.13964:", "Subsubsection",
 CellChangeTimes->{{3.9384374140128527`*^9, 3.938437440951729*^9}, {
  3.938437666171742*^9, 3.9384376669289837`*^9}, {3.*************^9, 
  3.9384377049000072`*^9}, {3.938437754588849*^9, 3.938437755372746*^9}, {
  3.93843778758041*^9, 
  3.938437803067082*^9}},ExpressionUUID->"28d255c2-f38d-4945-a5c2-\
46e3a82ac408"],

Cell[BoxData[
 RowBox[{
  RowBox[{"\[CapitalTheta]", "[", 
   RowBox[{
   "\[Theta]p_", ",", " ", "\[Theta]m_", ",", "\[Theta]s_", ",", 
    "\[Sigma]_"}], "]"}], ":=", " ", 
  RowBox[{
   RowBox[{"(", 
    RowBox[{
     SuperscriptBox[
      RowBox[{"(", 
       RowBox[{"Gamma", "[", 
        RowBox[{"2", "-", "\[Sigma]"}], "]"}], ")"}], "2"], 
     RowBox[{"Gamma", "[", 
      RowBox[{
       FractionBox["1", "2"], 
       RowBox[{"(", 
        RowBox[{"\[Theta]s", "+", "\[Sigma]"}], ")"}]}], "]"}], 
     RowBox[{"Gamma", "[", 
      RowBox[{
       FractionBox["1", "2"], 
       RowBox[{"(", 
        RowBox[{"\[Theta]m", "+", "\[Theta]p", "+", "\[Sigma]"}], ")"}]}], 
      "]"}], 
     RowBox[{"Gamma", "[", 
      RowBox[{
       FractionBox["1", "2"], 
       RowBox[{"(", 
        RowBox[{
         RowBox[{"-", "\[Theta]m"}], "+", "\[Theta]p", "+", "\[Sigma]"}], 
        ")"}]}], "]"}]}], ")"}], "/", 
   RowBox[{"(", 
    RowBox[{
     SuperscriptBox[
      RowBox[{"(", 
       RowBox[{"Gamma", "[", "\[Sigma]", "]"}], ")"}], "2"], 
     RowBox[{"Gamma", "[", 
      RowBox[{"1", "+", 
       RowBox[{
        FractionBox["1", "2"], 
        RowBox[{"(", 
         RowBox[{"\[Theta]s", "-", "\[Sigma]"}], ")"}]}]}], "]"}], 
     RowBox[{"Gamma", "[", 
      RowBox[{"1", "+", 
       RowBox[{
        FractionBox["1", "2"], 
        RowBox[{"(", 
         RowBox[{"\[Theta]m", "+", "\[Theta]p", "-", "\[Sigma]"}], ")"}]}]}], 
      "]"}], 
     RowBox[{"Gamma", "[", 
      RowBox[{"1", "+", 
       RowBox[{
        FractionBox["1", "2"], 
        RowBox[{"(", 
         RowBox[{
          RowBox[{"-", "\[Theta]m"}], "+", "\[Theta]p", "-", "\[Sigma]"}], 
         ")"}]}]}], "]"}]}], ")"}]}]}]], "Input",
 CellChangeTimes->{{3.9384177894497547`*^9, 3.9384177900765057`*^9}, {
  3.938417883452984*^9, 3.938417887647436*^9}},
 CellLabel->"In[10]:=",ExpressionUUID->"bab07929-36a8-45ca-9d03-e622ebb1f052"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{"Series", "[", 
  RowBox[{
   RowBox[{"\[CapitalTheta]", "[", 
    RowBox[{
    "\[Theta]p", ",", " ", "\[Theta]m", ",", "\[Theta]s", ",", "\[Sigma]"}], 
    "]"}], ",", 
   RowBox[{"{", 
    RowBox[{"\[Delta]", ",", "0", ",", "0"}], "}"}]}], "]"}]], "Input",
 CellChangeTimes->{{3.938414088183329*^9, 3.938414088768717*^9}, {
  3.9384141745343037`*^9, 3.938414178144597*^9}},
 CellLabel->"In[11]:=",ExpressionUUID->"540fb893-f25f-4be7-af83-50edf2628b01"],

Cell[BoxData[
 InterpretationBox[
  RowBox[{
   FractionBox[
    RowBox[{
     RowBox[{"Gamma", "[", 
      RowBox[{
       FractionBox["1", "2"], " ", 
       RowBox[{"(", 
        RowBox[{"1", "+", 
         RowBox[{"2", " ", "\[ImaginaryI]", " ", "m"}], "-", "\[Sigma]0"}], 
        ")"}]}], "]"}], " ", 
     RowBox[{"Gamma", "[", 
      RowBox[{
       FractionBox["1", "2"], " ", 
       RowBox[{"(", 
        RowBox[{"1", "+", 
         RowBox[{"4", " ", "\[ImaginaryI]", " ", "z1"}], "-", "\[Sigma]0"}], 
        ")"}]}], "]"}], " ", 
     RowBox[{"Gamma", "[", 
      RowBox[{
       FractionBox["1", "2"], " ", 
       RowBox[{"(", 
        RowBox[{"1", "-", 
         FractionBox[
          RowBox[{"2", " ", 
           RowBox[{"(", 
            RowBox[{
             SuperscriptBox["m", "2"], "-", 
             RowBox[{"2", " ", 
              SuperscriptBox["\[Mu]", "2"]}]}], ")"}]}], 
          SqrtBox[
           RowBox[{
            RowBox[{"-", 
             SuperscriptBox["m", "2"]}], "+", 
            RowBox[{"4", " ", 
             SuperscriptBox["\[Mu]", "2"]}]}]]], "-", "\[Sigma]0"}], ")"}]}], 
      "]"}], " ", 
     SuperscriptBox[
      RowBox[{"Gamma", "[", 
       RowBox[{"1", "+", "\[Sigma]0"}], "]"}], "2"]}], 
    RowBox[{
     SuperscriptBox[
      RowBox[{"Gamma", "[", 
       RowBox[{"1", "-", "\[Sigma]0"}], "]"}], "2"], " ", 
     RowBox[{"Gamma", "[", 
      RowBox[{
       FractionBox["1", "2"], " ", 
       RowBox[{"(", 
        RowBox[{"1", "+", 
         RowBox[{"2", " ", "\[ImaginaryI]", " ", "m"}], "+", "\[Sigma]0"}], 
        ")"}]}], "]"}], " ", 
     RowBox[{"Gamma", "[", 
      RowBox[{
       FractionBox["1", "2"], " ", 
       RowBox[{"(", 
        RowBox[{"1", "+", 
         RowBox[{"4", " ", "\[ImaginaryI]", " ", "z1"}], "+", "\[Sigma]0"}], 
        ")"}]}], "]"}], " ", 
     RowBox[{"Gamma", "[", 
      RowBox[{"1", "+", 
       RowBox[{
        FractionBox["1", "2"], " ", 
        RowBox[{"(", 
         RowBox[{
          RowBox[{"-", "1"}], "-", 
          FractionBox[
           RowBox[{"2", " ", 
            RowBox[{"(", 
             RowBox[{
              SuperscriptBox["m", "2"], "-", 
              RowBox[{"2", " ", 
               SuperscriptBox["\[Mu]", "2"]}]}], ")"}]}], 
           SqrtBox[
            RowBox[{
             RowBox[{"-", 
              SuperscriptBox["m", "2"]}], "+", 
             RowBox[{"4", " ", 
              SuperscriptBox["\[Mu]", "2"]}]}]]], "+", "\[Sigma]0"}], 
         ")"}]}]}], "]"}]}]], "+", 
   InterpretationBox[
    SuperscriptBox[
     RowBox[{"O", "[", "\[Delta]", "]"}], "1"],
    SeriesData[$CellContext`\[Delta], 0, {}, 0, 1, 1],
    Editable->False]}],
  SeriesData[$CellContext`\[Delta], 0, {
   Gamma[1 - $CellContext`\[Sigma]0]^(-2) 
    Gamma[Rational[1, 2] (1 + 
       Complex[0, 2] $CellContext`m - $CellContext`\[Sigma]0)] 
    Gamma[Rational[1, 2] (1 + 
       Complex[0, 4] $CellContext`z1 - $CellContext`\[Sigma]0)] 
    Gamma[Rational[1, 2] (1 - 
       2 ($CellContext`m^2 - 
        2 $CellContext`\[Mu]^2) (-$CellContext`m^2 + 4 $CellContext`\[Mu]^2)^
        Rational[-1, 2] - $CellContext`\[Sigma]0)] 
    Gamma[1 + $CellContext`\[Sigma]0]^2 
    Gamma[Rational[1, 2] (1 + 
        Complex[0, 2] $CellContext`m + $CellContext`\[Sigma]0)]^(-1) 
    Gamma[Rational[1, 2] (1 + 
        Complex[0, 4] $CellContext`z1 + $CellContext`\[Sigma]0)]^(-1)/Gamma[
    1 + Rational[1, 2] (-1 - 
       2 ($CellContext`m^2 - 
        2 $CellContext`\[Mu]^2) (-$CellContext`m^2 + 4 $CellContext`\[Mu]^2)^
        Rational[-1, 2] + $CellContext`\[Sigma]0)]}, 0, 1, 1],
  Editable->False]], "Output",
 CellChangeTimes->{
  3.93841409185537*^9, {3.9384141305815363`*^9, 3.9384141426326942`*^9}, 
   3.938414175180788*^9, 3.938414300448764*^9, {3.938417796310425*^9, 
   3.938417829569656*^9}, 3.93841791372836*^9, 3.938418074643168*^9, 
   3.9384182198204613`*^9, 3.9384439321886997`*^9},
 CellLabel->"Out[11]=",ExpressionUUID->"0c95c917-e6cd-4720-a634-b2a97e7485a8"]
}, Open  ]],

Cell["\<\
Multiplying both expansions, we obtain the following simplified expression at \
zero order in \[Delta]:\
\>", "Text",
 CellChangeTimes->{{3.938180285401119*^9, 3.938180410927924*^9}, {
   3.938180490794709*^9, 3.93818062945115*^9}, {3.938180758441063*^9, 
   3.938180768332391*^9}, {3.938181466888914*^9, 3.938181469311522*^9}, {
   3.938181507724976*^9, 3.938181513002969*^9}, {3.938194678461656*^9, 
   3.9381946798735332`*^9}, {3.9382022747357073`*^9, 3.938202277023535*^9}, {
   3.9382023999464417`*^9, 3.938202400457551*^9}, {3.938203124414137*^9, 
   3.938203127989369*^9}, {3.93820387134199*^9, 3.938203888512754*^9}, 
   3.9382639966686563`*^9, {3.9382710836217527`*^9, 3.93827108801256*^9}, {
   3.938272971570373*^9, 3.9382731260545607`*^9}, {3.9382734477045937`*^9, 
   3.938273543225835*^9}, {3.938273633437293*^9, 3.9382736687623672`*^9}, {
   3.9382737150333557`*^9, 3.9382737407244864`*^9}, {3.93841222204696*^9, 
   3.938412228617383*^9}, {3.938444087066269*^9, 3.9384441286219187`*^9}, {
   3.938444228845336*^9, 3.938444232780086*^9}, {3.9384443422113733`*^9, 
   3.9384443627232428`*^9}, {3.9384444188642397`*^9, 3.938444432226121*^9}},
 TextJustification->
  0.75,ExpressionUUID->"158edd35-cecc-4132-a4d3-66c223c62325"],

Cell[BoxData[{
 RowBox[{"essExpn", ":=", 
  RowBox[{
   RowBox[{"-", 
    SuperscriptBox["\[ExponentialE]", 
     RowBox[{
      RowBox[{"-", "\[ImaginaryI]"}], " ", "\[Pi]", " ", 
      RowBox[{"(", 
       RowBox[{"1", "+", "\[Sigma]0"}], ")"}]}]]}], 
   RowBox[{"(", 
    RowBox[{
     RowBox[{"(", 
      RowBox[{
       RowBox[{"Gamma", "[", 
        RowBox[{
         FractionBox["1", "2"], " ", 
         RowBox[{"(", 
          RowBox[{"1", "+", "\[Gamma]", "+", "\[Sigma]0"}], ")"}]}], "]"}], 
       RowBox[{"Gamma", "[", 
        RowBox[{
         RowBox[{
          FractionBox["1", "2"], " ", 
          RowBox[{"(", 
           RowBox[{"1", "+", "\[Sigma]0"}], ")"}]}], "-", 
         RowBox[{"\[ImaginaryI]", " ", "m"}]}], "]"}]}], ")"}], "/", 
     RowBox[{"(", 
      RowBox[{
       RowBox[{"Gamma", "[", 
        RowBox[{
         FractionBox["1", "2"], " ", 
         RowBox[{"(", 
          RowBox[{"1", "+", "\[Gamma]", "-", "\[Sigma]0"}], ")"}]}], "]"}], 
       " ", 
       RowBox[{"Gamma", "[", 
        RowBox[{
         RowBox[{
          FractionBox["1", "2"], " ", 
          RowBox[{"(", 
           RowBox[{"1", "-", "\[Sigma]0"}], ")"}]}], "-", 
         RowBox[{"\[ImaginaryI]", " ", "m"}]}], "]"}]}], ")"}]}], ")"}], " ", 
   
   FractionBox[
    RowBox[{"Gamma", "[", " ", 
     RowBox[{
      FractionBox["1", "2"], " ", 
      RowBox[{"(", 
       RowBox[{"1", "-", 
        RowBox[{"4", "\[ImaginaryI]", " ", "z1"}], "+", "\[Sigma]0"}], 
       ")"}]}], "]"}], 
    RowBox[{"Gamma", "[", 
     RowBox[{
      FractionBox["1", "2"], 
      RowBox[{"(", 
       RowBox[{"1", "-", 
        RowBox[{"4", " ", "\[ImaginaryI]", " ", "z1"}], "-", "\[Sigma]0"}], 
       ")"}]}], "]"}]], 
   FractionBox[
    SuperscriptBox[
     RowBox[{"Gamma", "[", 
      RowBox[{"1", "-", "\[Sigma]0"}], "]"}], "2"], 
    RowBox[{" ", 
     SuperscriptBox[
      RowBox[{"Gamma", "[", 
       RowBox[{"1", "+", "\[Sigma]0"}], "]"}], "2"]}]]}]}], "\n", 
 RowBox[{"\[Gamma]", ":=", 
  FractionBox[
   RowBox[{"2", " ", 
    RowBox[{"(", 
     RowBox[{
      SuperscriptBox["m", "2"], "-", 
      RowBox[{"2", " ", 
       SuperscriptBox["\[Mu]", "2"]}]}], ")"}]}], 
   SqrtBox[
    RowBox[{
     RowBox[{"4", " ", 
      SuperscriptBox["\[Mu]", "2"]}], "-", 
     SuperscriptBox["m", "2"]}]]]}]}], "Input",
 CellChangeTimes->{{3.938202018002275*^9, 3.9382020207422733`*^9}, 
   3.938275670070547*^9, {3.938413890737873*^9, 3.938413892181719*^9}, 
   3.938413987293583*^9, {3.9384182444283543`*^9, 3.9384182804603863`*^9}, {
   3.938444461823824*^9, 3.938444486005001*^9}, {3.938444578972419*^9, 
   3.9384445802026453`*^9}},ExpressionUUID->"7cb2ff18-6aec-4760-86f6-\
cb234b04b8ba"],

Cell[TextData[{
 "Substituting the expression above in the Eq. (19) and considering  ",
 StyleBox["u0 =", "Input"],
 StyleBox[Cell[BoxData[
  RowBox[{" ", 
   InterpretationBox[
    RowBox[{
     RowBox[{"2", " ", 
      SqrtBox[
       RowBox[{
        RowBox[{"-", 
         SuperscriptBox["m", "2"]}], "+", 
        RowBox[{"4", " ", 
         SuperscriptBox["\[Mu]", "2"]}]}]], " ", "\[Delta]"}], "+", 
     InterpretationBox[
      SuperscriptBox[
       RowBox[{"O", "[", "\[Delta]", "]"}], "2"],
      SeriesData[$CellContext`\[Delta], 0, {}, 1, 2, 1],
      Editable->False]}],
    SeriesData[$CellContext`\[Delta], 0, {
     2 (-$CellContext`m^2 + 4 $CellContext`\[Mu]^2)^Rational[1, 2]}, 1, 2, 1],
    
    Editable->False]}]], "Input",
  CellChangeTimes->{3.938444843782221*^9},ExpressionUUID->
  "f474d036-917f-42bd-8431-03be69e74806"], "Input"],
 ", we obtain the following expression at lower order in \[Delta]:"
}], "Text",
 CellChangeTimes->{{3.938180285401119*^9, 3.938180410927924*^9}, {
   3.938180490794709*^9, 3.93818062945115*^9}, {3.938180758441063*^9, 
   3.938180768332391*^9}, {3.938181466888914*^9, 3.938181469311522*^9}, {
   3.938181507724976*^9, 3.938181513002969*^9}, {3.938194678461656*^9, 
   3.9381946798735332`*^9}, {3.9382022747357073`*^9, 3.938202277023535*^9}, {
   3.9382023999464417`*^9, 3.938202400457551*^9}, {3.938203124414137*^9, 
   3.938203127989369*^9}, {3.93820387134199*^9, 3.938203888512754*^9}, 
   3.9382639966686563`*^9, {3.9382710836217527`*^9, 3.93827108801256*^9}, {
   3.938272971570373*^9, 3.9382731260545607`*^9}, {3.9382734477045937`*^9, 
   3.938273543225835*^9}, {3.938273633437293*^9, 3.9382736687623672`*^9}, {
   3.9382737150333557`*^9, 3.9382737407244864`*^9}, {3.93841222204696*^9, 
   3.938412228617383*^9}, {3.938444087066269*^9, 3.9384441286219187`*^9}, {
   3.938444228845336*^9, 3.938444232780086*^9}, {3.9384443422113733`*^9, 
   3.9384443627232428`*^9}, {3.9384444188642397`*^9, 3.938444432226121*^9}, {
   3.9384446899431267`*^9, 3.938444697239278*^9}, {3.9384447586086082`*^9, 
   3.938444808420787*^9}, {3.9384448637340918`*^9, 3.938444962211627*^9}, {
   3.938445252823803*^9, 3.938445253449243*^9}},
 TextJustification->
  0.75,ExpressionUUID->"c8f0dfbb-dd4f-411d-8797-2e460dcca9a3"],

Cell[BoxData[
 RowBox[{"FinalResult", ":=", " ", 
  RowBox[{
   RowBox[{"-", 
    SuperscriptBox["\[ExponentialE]", 
     RowBox[{
      RowBox[{"-", "\[ImaginaryI]"}], " ", "\[Pi]", " ", 
      RowBox[{"(", 
       RowBox[{"1", "+", "\[Sigma]0"}], ")"}]}]]}], 
   SuperscriptBox[
    RowBox[{"(", 
     RowBox[{"2", " ", 
      SqrtBox[
       RowBox[{
        RowBox[{"-", 
         SuperscriptBox["m", "2"]}], "+", 
        RowBox[{"4", " ", 
         SuperscriptBox["\[Mu]", "2"]}]}]], " ", "\[Delta]"}], ")"}], 
    "\[Sigma]0"], " ", 
   RowBox[{"(", 
    RowBox[{
     RowBox[{"(", 
      RowBox[{
       RowBox[{"(", 
        RowBox[{
         RowBox[{"Gamma", "[", 
          RowBox[{
           FractionBox["1", "2"], " ", 
           RowBox[{"(", 
            RowBox[{"1", "+", "\[Gamma]", "+", "\[Sigma]0"}], ")"}]}], "]"}], 
         
         RowBox[{"Gamma", "[", 
          RowBox[{
           RowBox[{
            FractionBox["1", "2"], " ", 
            RowBox[{"(", 
             RowBox[{"1", "+", "\[Sigma]0"}], ")"}]}], "-", 
           RowBox[{"\[ImaginaryI]", " ", "m"}]}], "]"}]}], ")"}], "/", 
       RowBox[{"(", 
        RowBox[{
         RowBox[{"Gamma", "[", 
          RowBox[{
           FractionBox["1", "2"], " ", 
           RowBox[{"(", 
            RowBox[{"1", "+", "\[Gamma]", "-", "\[Sigma]0"}], ")"}]}], "]"}], 
         " ", 
         RowBox[{"Gamma", "[", 
          RowBox[{
           RowBox[{
            FractionBox["1", "2"], " ", 
            RowBox[{"(", 
             RowBox[{"1", "-", "\[Sigma]0"}], ")"}]}], "-", 
           RowBox[{"\[ImaginaryI]", " ", "m"}]}], "]"}]}], ")"}]}], ")"}], 
     " ", 
     FractionBox[
      RowBox[{"Gamma", "[", " ", 
       RowBox[{
        FractionBox["1", "2"], " ", 
        RowBox[{"(", 
         RowBox[{"1", "-", 
          RowBox[{"4", "\[ImaginaryI]", " ", "z1"}], "+", "\[Sigma]0"}], 
         ")"}]}], "]"}], 
      RowBox[{"Gamma", "[", 
       RowBox[{
        FractionBox["1", "2"], 
        RowBox[{"(", 
         RowBox[{"1", "-", 
          RowBox[{"4", " ", "\[ImaginaryI]", " ", "z1"}], "-", "\[Sigma]0"}], 
         ")"}]}], "]"}]], 
     FractionBox[
      SuperscriptBox[
       RowBox[{"Gamma", "[", 
        RowBox[{"1", "-", "\[Sigma]0"}], "]"}], "2"], 
      RowBox[{" ", 
       SuperscriptBox[
        RowBox[{"Gamma", "[", 
         RowBox[{"1", "+", "\[Sigma]0"}], "]"}], "2"]}]]}], 
    ")"}]}]}]], "Input",
 CellChangeTimes->{
  3.938444854864478*^9, {3.9384450703713217`*^9, 3.938445071407859*^9}, {
   3.938445428450767*^9, 
   3.938445429050956*^9}},ExpressionUUID->"0349a5e1-090d-4449-9b48-\
cb465cab1f8a"],

Cell[TextData[{
 "From poles of the gamma function",
 Cell[BoxData[
  RowBox[{" ", 
   RowBox[{"Gamma", "[", " ", 
    RowBox[{
     FractionBox["1", "2"], " ", 
     RowBox[{"(", 
      RowBox[{"1", "-", 
       RowBox[{"4", "\[ImaginaryI]", " ", "z1"}], "+", "\[Sigma]0"}], ")"}]}],
     "]"}]}]],ExpressionUUID->"a5f04c3e-7edf-439a-9bcd-9ebb76ca82ce"],
 ", we arrive at the following asymptotic expression for the overtone \
frequencies:"
}], "Text",
 CellChangeTimes->{{3.938180285401119*^9, 3.938180410927924*^9}, {
   3.938180490794709*^9, 3.93818062945115*^9}, {3.938180758441063*^9, 
   3.938180768332391*^9}, {3.938181466888914*^9, 3.938181469311522*^9}, {
   3.938181507724976*^9, 3.938181513002969*^9}, {3.938194678461656*^9, 
   3.9381946798735332`*^9}, {3.9382022747357073`*^9, 3.938202277023535*^9}, {
   3.9382023999464417`*^9, 3.938202400457551*^9}, {3.938203124414137*^9, 
   3.938203127989369*^9}, {3.93820387134199*^9, 3.938203888512754*^9}, 
   3.9382639966686563`*^9, {3.9382710836217527`*^9, 3.93827108801256*^9}, {
   3.938272971570373*^9, 3.9382731260545607`*^9}, {3.9382734477045937`*^9, 
   3.938273543225835*^9}, {3.938273633437293*^9, 3.9382736687623672`*^9}, {
   3.9382737150333557`*^9, 3.9382737407244864`*^9}, {3.93841222204696*^9, 
   3.938412228617383*^9}, {3.938444087066269*^9, 3.9384441286219187`*^9}, {
   3.938444228845336*^9, 3.938444232780086*^9}, {3.9384443422113733`*^9, 
   3.9384443627232428`*^9}, {3.9384444188642397`*^9, 3.938444432226121*^9}, {
   3.9384446899431267`*^9, 3.938444697239278*^9}, {3.9384447586086082`*^9, 
   3.938444808420787*^9}, {3.9384448637340918`*^9, 3.938444962211627*^9}, {
   3.938445107903228*^9, 3.9384451550106*^9}, {3.938445373931465*^9, 
   3.938445400802245*^9}},
 TextJustification->
  0.75,ExpressionUUID->"137ef649-c99b-4e67-954a-7d21d8e68a9c"],

Cell[BoxData[
 RowBox[{
  RowBox[{"omega", "[", 
   RowBox[{"m_", ",", "\[Delta]_"}], "]"}], ":=", " ", 
  RowBox[{
   FractionBox["m", "2"], "-", 
   RowBox[{
    FractionBox["1", "4"], " ", "\[ImaginaryI]", " ", 
    RowBox[{"(", 
     RowBox[{"1", "+", 
      SqrtBox[
       RowBox[{"1", "-", 
        RowBox[{"7", " ", 
         SuperscriptBox["m", "2"]}], "+", 
        RowBox[{"4", " ", "\[Lambda]0"}], "+", 
        RowBox[{"4", " ", 
         SuperscriptBox["\[Mu]", "2"]}]}]]}], ")"}], " ", "\[Delta]"}], "-", 
   " ", 
   RowBox[{"\[ImaginaryI]", 
    FractionBox[
     RowBox[{"n", " ", "\[Delta]"}], "2"]}]}]}]], "Input",
 CellChangeTimes->{{3.9018013790069857`*^9, 3.901801386589744*^9}, {
  3.904206990357039*^9, 3.904207012265889*^9}, {3.906789680241476*^9, 
  3.9067896810811872`*^9}, {3.9175855699433413`*^9, 3.917585570212309*^9}, {
  3.917585624718053*^9, 3.917585625404077*^9}, {3.926535843512505*^9, 
  3.926535848655599*^9}, {3.9270549267832613`*^9, 3.927054936384255*^9}},
 CellLabel->"In[1]:=",ExpressionUUID->"8e4982fe-867d-40b7-ba51-42db32dd4318"],

Cell[TextData[StyleBox["Verification of the above expression using numerical \
results obtained from the isomonodromy method implemented in Julia.", \
"Subsection"]], "Text",
 CellChangeTimes->{{3.829904381304707*^9, 3.8299044057379913`*^9}, {
   3.829910112120483*^9, 3.8299101126408663`*^9}, {3.901801427105616*^9, 
   3.901801455358479*^9}, 3.904207315905404*^9, {3.9384137014319963`*^9, 
   3.9384137016197453`*^9}, {3.938441437294977*^9, 3.938441438617861*^9}, {
   3.938445451298828*^9, 3.938445513113661*^9}, 
   3.938445551264681*^9},ExpressionUUID->"2f799c9f-c272-48a1-8c6b-\
b46754589bf9"],

Cell[BoxData[
 StyleBox[
  RowBox[{
   RowBox[{"Case", " ", "\[Mu]"}], "=", 
   RowBox[{
    RowBox[{"0.4", " ", "and", " ", 
     RowBox[{"a", "/", "M"}]}], "=", "0.9999"}]}], "Subsection"]], "Input",
 CellChangeTimes->{{3.917436325744729*^9, 3.917436326055889*^9}, 
   3.920567206235384*^9, 3.920569406386567*^9, {3.9205730507317038`*^9, 
   3.920573050976008*^9}, {3.926535756757712*^9, 3.9265357641424913`*^9}, {
   3.92705382868356*^9, 
   3.927053832045521*^9}},ExpressionUUID->"7e3f0040-d93e-4b9f-aaef-\
52ae7d92080c"],

Cell[BoxData[{
 RowBox[{"\[Lambda]0", ":=", 
  RowBox[{"1.9818925875373423", "+", 
   RowBox[{
   "0.001855202741660516", "\[ImaginaryI]"}]}]}], "\[IndentingNewLine]", 
 RowBox[{"m", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"n", ":=", "0"}], "\[IndentingNewLine]", 
 RowBox[{"\[Delta]", ":=", 
  RowBox[{"ArcCos", "[", "0.9999", "]"}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Mu]", ":=", "0.4"}]}], "Input",
 CellChangeTimes->{{3.8302854864701757`*^9, 3.830285491653075*^9}, {
   3.901801477300082*^9, 3.9018015543030567`*^9}, {3.9018024780612373`*^9, 
   3.901802483901363*^9}, {3.917436385828042*^9, 3.917436409502087*^9}, {
   3.917436459896915*^9, 3.917436495142379*^9}, 3.920567201651334*^9, 
   3.920567308183551*^9, 3.9205694078245897`*^9, 3.9205694951615868`*^9, 
   3.9205714443272038`*^9, {3.920572999074464*^9, 3.920573016611368*^9}, {
   3.920573063090478*^9, 3.9205730730567713`*^9}, {3.9205732106563463`*^9, 
   3.9205732160655403`*^9}, {3.9205732528444242`*^9, 
   3.9205732589444942`*^9}, {3.920705110842387*^9, 3.920705126149835*^9}, {
   3.9265357732182903`*^9, 3.926535798601645*^9}, {3.926535850139503*^9, 
   3.92653588039688*^9}, {3.9270538166891623`*^9, 3.9270538221100817`*^9}, {
   3.9270538840755863`*^9, 3.927053914092667*^9}, {3.927054938357991*^9, 
   3.927054940156496*^9}, {3.938445672984806*^9, 3.938445673303816*^9}},
 CellLabel->"In[2]:=",ExpressionUUID->"f82295ee-**************-51f0eaa0fd6f"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{
  RowBox[{"omega", "[", 
   RowBox[{"m", ",", "\[Delta]"}], "]"}], "   ", 
  RowBox[{"(*", 
   RowBox[{"0.5004550633270014", "-", 
    RowBox[{"0.00923116703609498", "im"}]}], "*)"}]}]], "Input",
 CellChangeTimes->{{3.830286569452341*^9, 3.8302865917630796`*^9}, {
   3.830286624504738*^9, 3.830286629540207*^9}, {3.90180161733075*^9, 
   3.901801636031354*^9}, {3.9018025470356483`*^9, 3.90180256974561*^9}, {
   3.917428893180361*^9, 3.917428893624271*^9}, {3.9174364000719423`*^9, 
   3.917436401538619*^9}, 3.917436468038487*^9, 3.920567318178172*^9, 
   3.9205694891056347`*^9, 3.920571450716528*^9, 3.9205729910542393`*^9, 
   3.920705135929735*^9, 3.926535781376184*^9, 3.927053874526847*^9, {
   3.9270549436159887`*^9, 3.9270549440244293`*^9}},
 CellLabel->"In[7]:=",ExpressionUUID->"997d6ce4-1709-4273-8601-13a71fe79aa9"],

Cell[BoxData[
 RowBox[{"0.5000081868789568`", "\[VeryThinSpace]", "-", 
  RowBox[{"0.009200828705318168`", " ", "\[ImaginaryI]"}]}]], "Output",
 CellChangeTimes->{
  3.926535813067821*^9, {3.926535861675561*^9, 3.926535884523335*^9}, {
   3.9270538984243717`*^9, 3.927053916590218*^9}, 3.927054962256755*^9, 
   3.9384458682531137`*^9},
 CellLabel->"Out[7]=",ExpressionUUID->"17e91640-4942-47ce-b432-d9ed4536362c"]
}, Open  ]],

Cell[BoxData[
 RowBox[{"Clear", "[", 
  RowBox[{"\[Mu]", ",", "\[Lambda]0"}], "]"}]], "Input",
 CellLabel->"In[8]:=",ExpressionUUID->"d120976b-2849-4a15-a45a-b04c27563d5a"],

Cell[BoxData[
 StyleBox[
  RowBox[{
   RowBox[{"Case", " ", "\[Mu]"}], "=", 
   RowBox[{
    RowBox[{"0.5", " ", "and", " ", 
     RowBox[{"a", "/", "M"}]}], "=", "0.9999"}]}], "Subsection"]], "Input",
 CellChangeTimes->{{3.917436325744729*^9, 3.917436326055889*^9}, 
   3.920567206235384*^9, 3.920569406386567*^9, {3.9205730507317038`*^9, 
   3.920573050976008*^9}, {3.927055037904804*^9, 
   3.9270550564426737`*^9}},ExpressionUUID->"54e922f5-4b5c-4a0a-82d6-\
7995e2d3e3f2"],

Cell[BoxData[{
 RowBox[{"\[Lambda]0", ":=", 
  RowBox[{"1.9999530968941825", "+", 
   RowBox[{
   "0.001938705597795165", "\[ImaginaryI]"}]}]}], "\[IndentingNewLine]", 
 RowBox[{"m", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"\[Delta]", ":=", 
  RowBox[{"ArcCos", "[", "0.9999", "]"}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Mu]", ":=", "0.5"}]}], "Input",
 CellChangeTimes->{{3.8302854864701757`*^9, 3.830285491653075*^9}, {
   3.901801477300082*^9, 3.9018015543030567`*^9}, {3.9018024780612373`*^9, 
   3.901802483901363*^9}, {3.917436385828042*^9, 3.917436409502087*^9}, {
   3.917436459896915*^9, 3.917436495142379*^9}, 3.920567201651334*^9, 
   3.920567308183551*^9, 3.9205694078245897`*^9, 3.9205694951615868`*^9, 
   3.9205714443272038`*^9, {3.920572999074464*^9, 3.920573016611368*^9}, {
   3.920573063090478*^9, 3.9205730730567713`*^9}, {3.9205732106563463`*^9, 
   3.9205732160655403`*^9}, {3.9205732528444242`*^9, 
   3.9205732589444942`*^9}, {3.920705110842387*^9, 3.920705126149835*^9}, {
   3.9270540849926*^9, 3.9270540881550283`*^9}, 3.9270541525274553`*^9, {
   3.9270549486215973`*^9, 3.927054949340034*^9}, {3.93844567660808*^9, 
   3.9384456769276876`*^9}},
 CellLabel->"In[9]:=",ExpressionUUID->"700458f0-9b0b-4af0-9533-42fe1e54f279"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{
  RowBox[{"omega", "[", 
   RowBox[{"m", ",", "\[Delta]"}], "]"}], "   ", 
  RowBox[{"(*", 
   RowBox[{"0.5003304760488329", "-", 
    RowBox[{"0.009688958709604225", "im"}]}], "*)"}]}]], "Input",
 CellChangeTimes->{{3.830286569452341*^9, 3.8302865917630796`*^9}, {
   3.830286624504738*^9, 3.830286629540207*^9}, {3.90180161733075*^9, 
   3.901801636031354*^9}, {3.9018025470356483`*^9, 3.90180256974561*^9}, {
   3.917428893180361*^9, 3.917428893624271*^9}, {3.9174364000719423`*^9, 
   3.917436401538619*^9}, 3.917436468038487*^9, 3.920567318178172*^9, 
   3.9205694891056347`*^9, 3.920571450716528*^9, 3.9205729910542393`*^9, 
   3.920705135929735*^9, 3.927054144162085*^9, {3.927054946119734*^9, 
   3.9270549469703283`*^9}},
 CellLabel->"In[13]:=",ExpressionUUID->"2d0ee3c0-d989-4ae9-be18-8445e2250d77"],

Cell[BoxData[
 RowBox[{"0.5000079150393033`", "\[VeryThinSpace]", "-", 
  RowBox[{"0.009659152388093471`", " ", "\[ImaginaryI]"}]}]], "Output",
 CellChangeTimes->{3.9270541650450687`*^9, 3.927055050603806*^9, 
  3.938445884878031*^9},
 CellLabel->"Out[13]=",ExpressionUUID->"86f6df01-c2ab-4b42-a08a-a6340f3f7cc7"]
}, Open  ]],

Cell[BoxData[
 StyleBox[
  RowBox[{
   RowBox[{"Case", " ", "\[Mu]"}], "=", 
   RowBox[{
    RowBox[{"0.2", " ", "and", " ", 
     RowBox[{"a", "/", "M"}]}], "=", "0.9999"}]}], "Subsubsection"]], "Input",\

 CellChangeTimes->{{3.9042061699571533`*^9, 3.904206170090625*^9}, 
   3.90584410494022*^9, {3.9205694145774918`*^9, 3.920569417769615*^9}, {
   3.920705350903352*^9, 3.920705351226285*^9}, 
   3.938446194784946*^9},ExpressionUUID->"05ba4f1e-87cb-40c5-b4b2-\
75db4d06924e"],

Cell[BoxData[
 RowBox[{"Clear", "[", 
  RowBox[{"\[Mu]", ",", "\[Lambda]0"}], "]"}]], "Input",
 CellChangeTimes->{{3.904205995592761*^9, 3.904206004152631*^9}, {
  3.920567491817378*^9, 3.920567492536663*^9}},
 CellLabel->"In[31]:=",ExpressionUUID->"d042a024-845e-42e0-a998-9a11525a0799"],

Cell[BoxData[{
 RowBox[{"\[Lambda]0", ":=", 
  RowBox[{"1.9576606459039427", "+", 
   RowBox[{
   "0.0016912005183750722", "\[ImaginaryI]"}]}]}], "\[IndentingNewLine]", 
 RowBox[{"m", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"\[Delta]", ":=", 
  RowBox[{"ArcCos", "[", "0.9999", "]"}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Mu]", ":=", "0.2"}], "\[IndentingNewLine]", 
 RowBox[{"n", ":=", "0"}]}], "Input",
 CellChangeTimes->{{3.8302854864701757`*^9, 3.830285491653075*^9}, {
   3.901801477300082*^9, 3.9018015543030567`*^9}, {3.9018024780612373`*^9, 
   3.901802483901363*^9}, {3.904206024457182*^9, 3.904206027582178*^9}, {
   3.904206059414187*^9, 3.904206070972438*^9}, {3.905843756373814*^9, 
   3.9058437767745047`*^9}, {3.9058441086596127`*^9, 3.9058441096492777`*^9}, 
   3.9058441534617367`*^9, 3.920567580396964*^9, 3.920569424174428*^9, 
   3.920571666261539*^9, {3.920705353568074*^9, 3.9207053769968452`*^9}, {
   3.9220398894388857`*^9, 3.922039926272963*^9}, 3.922039968091255*^9, {
   3.927056444265089*^9, 3.9270564768810062`*^9}, {3.927056507158177*^9, 
   3.927056516825625*^9}, {3.938445689538828*^9, 3.938445692540024*^9}, {
   3.938445927551724*^9, 3.938445927862252*^9}},
 CellLabel->"In[38]:=",ExpressionUUID->"e4fc09e7-f2cb-4874-b92f-50f349a993ca"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{
  RowBox[{"omega", "[", 
   RowBox[{"m", ",", "\[Delta]"}], "]"}], "   ", 
  RowBox[{"(*", 
   RowBox[{"0.5007896200174453", "-", 
    RowBox[{"0.008363131361909968", "im"}]}], "*)"}]}]], "Input",
 CellChangeTimes->{{3.905843863444138*^9, 3.9058438657159233`*^9}, {
   3.90584414090906*^9, 3.905844141895784*^9}, {3.917428888464508*^9, 
   3.917428889234343*^9}, 3.920567571634564*^9, 3.9205716820976467`*^9, 
   3.920705363868638*^9, {3.922039898892603*^9, 3.922039900674036*^9}, {
   3.922039943955422*^9, 3.922039946044724*^9}, {3.9270564645479593`*^9, 
   3.927056479628469*^9}},
 CellLabel->"In[43]:=",ExpressionUUID->"1bccd56e-f95c-4367-a562-ba6a37fcc104"],

Cell[BoxData[
 RowBox[{"0.5000084759122604`", "\[VeryThinSpace]", "-", 
  RowBox[{"0.008523901656277524`", " ", "\[ImaginaryI]"}]}]], "Output",
 CellChangeTimes->{{3.922039975005946*^9, 3.922039985477642*^9}, 
   3.927056480120874*^9, {3.927056514299097*^9, 3.927056519432168*^9}, {
   3.938445924228404*^9, 3.938445930279231*^9}},
 CellLabel->"Out[43]=",ExpressionUUID->"8ef20ec9-2878-4988-94a0-ea1a4b4fdc01"]
}, Open  ]],

Cell[BoxData[
 StyleBox[
  RowBox[{
   RowBox[{"Case", " ", "\[Mu]"}], "=", 
   RowBox[{
    RowBox[{"0.3", " ", "and", " ", 
     RowBox[{"a", "/", "M"}]}], "=", "0.9999999"}]}], 
  "Subsubsection"]], "Input",
 CellChangeTimes->{{3.90420618901186*^9, 3.90420618955484*^9}, {
   3.920567233148254*^9, 3.920567236077881*^9}, 3.920569425915922*^9, {
   3.92070543625312*^9, 
   3.920705436591147*^9}},ExpressionUUID->"7b23a06d-e37b-478e-983c-\
dc10c1da9f9e"],

Cell[BoxData[
 RowBox[{"Clear", "[", 
  RowBox[{"\[Mu]", ",", "\[Lambda]0"}], "]"}]], "Input",
 CellChangeTimes->{{3.904205995592761*^9, 3.904206004152631*^9}},
 CellLabel->"In[44]:=",ExpressionUUID->"1fe9f47b-1814-47d6-8363-792bcf6bcd94"],

Cell[BoxData[{
 RowBox[{"\[Lambda]0", ":=", 
  RowBox[{"1.967882395540671", "+", 
   RowBox[{
   "0.00005617219844994744", "\[ImaginaryI]"}]}]}], "\[IndentingNewLine]", 
 RowBox[{"m", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"\[Delta]", ":=", 
  RowBox[{"ArcCos", "[", "0.9999999", "]"}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Mu]", ":=", "0.3"}], "\[IndentingNewLine]", 
 RowBox[{"n", ":=", "0"}]}], "Input",
 CellChangeTimes->{{3.8302854864701757`*^9, 3.830285491653075*^9}, {
   3.901801477300082*^9, 3.9018015543030567`*^9}, {3.9018024780612373`*^9, 
   3.901802483901363*^9}, {3.904206024457182*^9, 3.904206027582178*^9}, {
   3.904206059414187*^9, 3.904206070972438*^9}, {3.904206206880156*^9, 
   3.904206225512169*^9}, 3.917432392244761*^9, {3.92056723809007*^9, 
   3.920567245241831*^9}, 3.92056772490831*^9, 3.920569429079277*^9, {
   3.920571762448148*^9, 3.920571764904229*^9}, {3.920705439659277*^9, 
   3.9207054404789047`*^9}, {3.9207054741256657`*^9, 3.920705479032339*^9}, {
   3.938445695876514*^9, 3.938445696196641*^9}, 3.938445728102384*^9, {
   3.938445965803026*^9, 
   3.938445966205764*^9}},ExpressionUUID->"4c96ddf8-af91-4a7a-92ce-\
e73294147a29"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{
  RowBox[{"omega", "[", 
   RowBox[{"m", ",", "\[Delta]"}], "]"}], "   ", 
  RowBox[{"(*", 
   RowBox[{"0.5000004847219527", "-", 
    RowBox[{"0.00027880838774518705", "im"}]}], "*)"}]}]], "Input",
 CellChangeTimes->{{3.830286569452341*^9, 3.8302865917630796`*^9}, {
   3.830286624504738*^9, 3.830286629540207*^9}, {3.90180161733075*^9, 
   3.901801636031354*^9}, {3.9018025470356483`*^9, 3.90180256974561*^9}, 
   3.904206090875677*^9, 3.904206233309506*^9, {3.917428882397098*^9, 
   3.917428883533484*^9}, 3.920567732695382*^9, 3.920571782448523*^9, 
   3.920705465352536*^9, {3.9384459525732613`*^9, 3.938445958282084*^9}},
 CellLabel->"In[49]:=",ExpressionUUID->"9e57bb30-02bb-4c5e-b41d-529963db6730"],

Cell[BoxData[
 RowBox[{"0.5000000084082401`", "\[VeryThinSpace]", "-", 
  RowBox[{"0.00027881872930707275`", " ", "\[ImaginaryI]"}]}]], "Output",
 CellChangeTimes->{3.9384459588274403`*^9},
 CellLabel->"Out[49]=",ExpressionUUID->"057de5a9-e5fd-4fd7-ad29-31c131d26c9c"]
}, Open  ]],

Cell[BoxData[
 StyleBox[
  RowBox[{
   RowBox[{"Case", " ", "\[Mu]"}], "=", 
   RowBox[{
    RowBox[{"0.4", " ", "and", " ", 
     RowBox[{"a", "/", "M"}]}], "=", "0.9999999"}]}], 
  "Subsubsection"]], "Input",
 CellChangeTimes->{{3.9042067561650257`*^9, 3.904206756784425*^9}, {
   3.9205672539349203`*^9, 3.9205672562101803`*^9}, 3.920569432731267*^9, {
   3.920705514272285*^9, 
   3.92070551467726*^9}},ExpressionUUID->"caaf1bde-89c1-454e-b96f-\
9053d61a4167"],

Cell[BoxData[
 RowBox[{"Clear", "[", 
  RowBox[{"\[Mu]", ",", "\[Lambda]0"}], "]"}]], "Input",
 CellChangeTimes->{{3.904205995592761*^9, 3.904206004152631*^9}},
 CellLabel->"In[50]:=",ExpressionUUID->"ad0c46db-4131-4e48-ab35-a7cb134af1c1"],

Cell[BoxData[{
 RowBox[{"\[Lambda]0", ":=", " ", 
  RowBox[{"1.9819628253892814", "+", 
   RowBox[{
   "0.000058432755301271667", "\[ImaginaryI]"}]}]}], "\[IndentingNewLine]", 
 RowBox[{"m", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"\[Delta]", ":=", 
  RowBox[{"ArcCos", "[", "0.9999999", "]"}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Mu]", ":=", "0.4"}]}], "Input",
 CellChangeTimes->{{3.8302854864701757`*^9, 3.830285491653075*^9}, {
   3.901801477300082*^9, 3.9018015543030567`*^9}, {3.9018024780612373`*^9, 
   3.901802483901363*^9}, {3.904206024457182*^9, 3.904206027582178*^9}, {
   3.904206059414187*^9, 3.904206070972438*^9}, {3.904206206880156*^9, 
   3.904206225512169*^9}, {3.904206809443274*^9, 3.904206827348784*^9}, {
   3.905886567840459*^9, 3.905886591748254*^9}, {3.917428753659751*^9, 
   3.917428760296805*^9}, {3.9174311744782047`*^9, 3.917431192838435*^9}, {
   3.917432412666991*^9, 3.91743243078825*^9}, {3.9174377971847963`*^9, 
   3.9174378145695467`*^9}, {3.9174432167880983`*^9, 
   3.9174432379439507`*^9}, {3.9174433621187057`*^9, 3.917443375720592*^9}, {
   3.917444079713312*^9, 3.917444114540756*^9}, {3.920567259743636*^9, 
   3.920567271546133*^9}, 3.920567938641465*^9, 3.9205694351079597`*^9, {
   3.920571891773625*^9, 3.920571932174218*^9}, {3.920705516058875*^9, 
   3.920705550783771*^9}, {3.938445698983008*^9, 3.938445699234921*^9}, 
   3.938445730701436*^9},
 CellLabel->"In[51]:=",ExpressionUUID->"c627da7e-c1c4-46bb-ada9-3c0a918bc154"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{
  RowBox[{"omega", "[", 
   RowBox[{"m", ",", "\[Delta]"}], "]"}], "   ", 
  RowBox[{"(*", 
   RowBox[{"0.5000003885914877", "-", 
    RowBox[{"0.00029096218402325125", "im"}]}], "*)"}]}]], "Input",
 CellChangeTimes->{{3.830286569452341*^9, 3.8302865917630796`*^9}, {
   3.830286624504738*^9, 3.830286629540207*^9}, {3.90180161733075*^9, 
   3.901801636031354*^9}, {3.9018025470356483`*^9, 3.90180256974561*^9}, 
   3.904206090875677*^9, 3.904206233309506*^9, 3.904206801025178*^9, 
   3.905886575898189*^9, 3.917428770192122*^9, {3.917428815353129*^9, 
   3.9174288162398043`*^9}, 3.917431182434162*^9, 3.91743242158457*^9, {
   3.917437805472829*^9, 3.9174378083134327`*^9}, 3.917443225452177*^9, 
   3.9174433686914062`*^9, 3.917444087525659*^9, 3.920567945624741*^9, 
   3.920571941195787*^9, 3.920705537279481*^9, {3.93844602741122*^9, 
   3.938446028126108*^9}},
 CellLabel->"In[56]:=",ExpressionUUID->"24b79b7f-fc13-4852-925c-95771d8d27a1"],

Cell[BoxData[
 RowBox[{"0.500000008153732`", "\[VeryThinSpace]", "-", 
  RowBox[{"0.00029096294279415185`", " ", "\[ImaginaryI]"}]}]], "Output",
 CellChangeTimes->{{3.938446024810548*^9, 3.938446028657668*^9}},
 CellLabel->"Out[56]=",ExpressionUUID->"cd4c7c48-c692-4fc3-aa77-c818bf867e32"]
}, Open  ]],

Cell[BoxData[
 StyleBox[
  RowBox[{
   RowBox[{"Case", " ", "\[Mu]"}], "=", 
   RowBox[{
    RowBox[{"0.5", " ", "and", " ", 
     RowBox[{"a", "/", "M"}]}], "=", "0.9999999"}]}], 
  "Subsubsection"]], "Input",
 CellChangeTimes->{{3.9042067561650257`*^9, 3.904206756784425*^9}, {
   3.904206869414483*^9, 3.904206870821731*^9}, {3.920568101470871*^9, 
   3.920568104108789*^9}, 3.9205694380072203`*^9, {3.920705602688714*^9, 
   3.920705603233386*^9}},ExpressionUUID->"2af8fdf2-b255-41da-830d-\
66f207295279"],

Cell[BoxData[
 RowBox[{"Clear", "[", 
  RowBox[{"\[Mu]", ",", "\[Lambda]0"}], "]"}]], "Input",
 CellChangeTimes->{{3.904205995592761*^9, 3.904206004152631*^9}},
 CellLabel->"In[57]:=",ExpressionUUID->"9cd33f22-5c11-4e40-bf46-ac0bd7309225"],

Cell[BoxData[{
 RowBox[{"\[Lambda]0", ":=", 
  RowBox[{"1.9999999562819606", "+", 
   RowBox[{
   "0.0000610907130350442", "\[ImaginaryI]"}]}]}], "\[IndentingNewLine]", 
 RowBox[{"m", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"\[Delta]", ":=", 
  RowBox[{"ArcCos", "[", "0.9999999", "]"}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Mu]", ":=", "0.5"}]}], "Input",
 CellChangeTimes->{{3.8302854864701757`*^9, 3.830285491653075*^9}, {
   3.901801477300082*^9, 3.9018015543030567`*^9}, {3.9018024780612373`*^9, 
   3.901802483901363*^9}, {3.904206024457182*^9, 3.904206027582178*^9}, {
   3.904206059414187*^9, 3.904206070972438*^9}, {3.904206206880156*^9, 
   3.904206225512169*^9}, {3.904206809443274*^9, 3.904206827348784*^9}, {
   3.904206911738882*^9, 3.904206944727193*^9}, {3.920568105860104*^9, 
   3.920568132374135*^9}, 3.920569440409512*^9, 3.920572028482397*^9, {
   3.920705605697763*^9, 3.920705605964158*^9}, {3.92070564119238*^9, 
   3.920705646451679*^9}, {3.938445701849449*^9, 3.938445702149374*^9}, 
   3.938445733147766*^9},
 CellLabel->"In[58]:=",ExpressionUUID->"959453d1-08d8-4cef-baf7-eb5de6c4aceb"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{
  RowBox[{"omega", "[", 
   RowBox[{"m", ",", "\[Delta]"}], "]"}], "   ", 
  RowBox[{"(*", 
   RowBox[{"0.5000003140245548", "-", 
    RowBox[{"0.0003054534313614748", "im"}]}], "*)"}]}]], "Input",
 CellChangeTimes->{{3.830286569452341*^9, 3.8302865917630796`*^9}, {
   3.830286624504738*^9, 3.830286629540207*^9}, {3.90180161733075*^9, 
   3.901801636031354*^9}, {3.9018025470356483`*^9, 3.90180256974561*^9}, 
   3.904206090875677*^9, 3.904206233309506*^9, 3.904206801025178*^9, {
   3.904206928296254*^9, 3.904206933798353*^9}, {3.917585708004628*^9, 
   3.917585708541582*^9}, 3.9205681190270967`*^9, 3.9205720366351852`*^9, 
   3.920705633493965*^9, {3.9384460415280247`*^9, 3.9384460421755123`*^9}},
 CellLabel->"In[63]:=",ExpressionUUID->"f2278f0b-87a6-4f9c-acee-6c582f16b340"],

Cell[BoxData[
 RowBox[{"0.5000000078867775`", "\[VeryThinSpace]", "-", 
  RowBox[{"0.0003054525631670383`", " ", "\[ImaginaryI]"}]}]], "Output",
 CellChangeTimes->{{3.938446039709546*^9, 3.93844604270471*^9}},
 CellLabel->"Out[63]=",ExpressionUUID->"315bd5cd-07a3-4fad-96b9-c006cf0ba58e"]
}, Open  ]],

Cell[BoxData[
 StyleBox[
  RowBox[{
   RowBox[{"Case", " ", "\[Mu]"}], "=", 
   RowBox[{
    RowBox[{"0.6", " ", "and", " ", 
     RowBox[{"a", "/", "M"}]}], "=", "0.9999999"}]}], 
  "Subsubsection"]], "Input",
 CellChangeTimes->{{3.9042067561650257`*^9, 3.904206756784425*^9}, {
   3.904206869414483*^9, 3.904206870821731*^9}, {3.920568101470871*^9, 
   3.920568104108789*^9}, 3.9205694380072203`*^9, {3.920575196142747*^9, 
   3.920575196489513*^9}, {3.920705786779793*^9, 
   3.9207057870672007`*^9}},ExpressionUUID->"55ec6685-a988-4287-9f96-\
1ea655b61783"],

Cell[BoxData[
 RowBox[{"Clear", "[", 
  RowBox[{"\[Mu]", ",", "\[Lambda]0"}], "]"}]], "Input",
 CellChangeTimes->{{3.904205995592761*^9, 3.904206004152631*^9}},
 CellLabel->"In[64]:=",ExpressionUUID->"898b807a-e51e-4965-b6dd-cddf6e6d003c"],

Cell[BoxData[{
 RowBox[{"\[Lambda]0", ":=", 
  RowBox[{"2.0219448126732367", "+", 
   RowBox[{
   "0.00006403724562652496", "\[ImaginaryI]"}]}]}], "\[IndentingNewLine]", 
 RowBox[{"m", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"\[Delta]", ":=", 
  RowBox[{"ArcCos", "[", "0.9999999", "]"}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Mu]", ":=", "0.6"}]}], "Input",
 CellChangeTimes->{{3.8302854864701757`*^9, 3.830285491653075*^9}, {
   3.901801477300082*^9, 3.9018015543030567`*^9}, {3.9018024780612373`*^9, 
   3.901802483901363*^9}, {3.904206024457182*^9, 3.904206027582178*^9}, {
   3.904206059414187*^9, 3.904206070972438*^9}, {3.904206206880156*^9, 
   3.904206225512169*^9}, {3.904206809443274*^9, 3.904206827348784*^9}, {
   3.904206911738882*^9, 3.904206944727193*^9}, {3.920568105860104*^9, 
   3.920568132374135*^9}, 3.920569440409512*^9, 3.920572028482397*^9, {
   3.9205752023445673`*^9, 3.9205752026641903`*^9}, 3.920575235441943*^9, {
   3.920575480889566*^9, 3.92057549851443*^9}, {3.920705811779914*^9, 
   3.920705822213958*^9}, {3.938445704371516*^9, 3.938445704613385*^9}, 
   3.93844573543567*^9},
 CellLabel->"In[65]:=",ExpressionUUID->"aaaa8e85-8775-47a5-95b1-77597699cddd"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{
  RowBox[{"omega", "[", 
   RowBox[{"m", ",", "\[Delta]"}], "]"}], "   ", 
  RowBox[{"(*", 
   RowBox[{"0.5000002597316802", "-", 
    RowBox[{"0.0003217972015290489", "im"}]}], "*)"}]}]], "Input",
 CellChangeTimes->{{3.830286569452341*^9, 3.8302865917630796`*^9}, {
   3.830286624504738*^9, 3.830286629540207*^9}, {3.90180161733075*^9, 
   3.901801636031354*^9}, {3.9018025470356483`*^9, 3.90180256974561*^9}, 
   3.904206090875677*^9, 3.904206233309506*^9, 3.904206801025178*^9, {
   3.904206928296254*^9, 3.904206933798353*^9}, {3.917585708004628*^9, 
   3.917585708541582*^9}, 3.9205681190270967`*^9, 3.9205720366351852`*^9, 
   3.920575244015945*^9, 3.920575491840251*^9, 3.920705803718689*^9, {
   3.938446052826041*^9, 3.938446053348699*^9}},
 CellLabel->"In[69]:=",ExpressionUUID->"159ff6ec-8600-4c22-ae8b-f1a60408818d"],

Cell[BoxData[
 RowBox[{"0.5000000076237202`", "\[VeryThinSpace]", "-", 
  RowBox[{"0.0003217968315877008`", " ", "\[ImaginaryI]"}]}]], "Output",
 CellChangeTimes->{3.9384460538236933`*^9},
 CellLabel->"Out[69]=",ExpressionUUID->"dcdc6de5-ad57-4d11-9a64-b3c44b147efb"]
}, Open  ]]
}, Open  ]],

Cell[CellGroupData[{

Cell["Case \[Mu] = 0.0 l=m=1 and a/M = 0.999999", "Subsubsection",
 CellChangeTimes->{{3.9042067561650257`*^9, 3.904206756784425*^9}, {
   3.904206869414483*^9, 3.904206870821731*^9}, {3.904207047496584*^9, 
   3.904207048734906*^9}, {3.912236453516667*^9, 3.912236462654896*^9}, {
   3.92074010903377*^9, 3.9207401203926287`*^9}, {3.920971762557708*^9, 
   3.920971765614007*^9}, {3.922643906038685*^9, 3.922643910585328*^9}, 
   3.938446151649838*^9},ExpressionUUID->"fac1d365-d4b7-42f4-a177-\
e64491fe2d02"],

Cell[BoxData[
 RowBox[{"Clear", "[", 
  RowBox[{"\[Mu]", ",", "\[Lambda]0"}], "]"}]], "Input",
 CellChangeTimes->{{3.904205995592761*^9, 3.904206004152631*^9}},
 CellLabel->"In[75]:=",ExpressionUUID->"8da987ca-2f13-441d-b1e3-278252b0eac6"],

Cell[BoxData[{
 RowBox[{"\[Lambda]0", ":=", 
  RowBox[{"1.9819619628821035", "+", 
   RowBox[{
   "0.00032679787853726364", "\[ImaginaryI]"}]}]}], "\[IndentingNewLine]", 
 RowBox[{"m", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"n", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"\[Delta]", ":=", 
  RowBox[{"ArcCos", "[", "0.999999", "]"}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Mu]", ":=", "0.4"}]}], "Input",
 CellChangeTimes->{{3.8302854864701757`*^9, 3.830285491653075*^9}, {
   3.901801477300082*^9, 3.9018015543030567`*^9}, {3.9018024780612373`*^9, 
   3.901802483901363*^9}, {3.904206024457182*^9, 3.904206027582178*^9}, {
   3.904206059414187*^9, 3.904206070972438*^9}, {3.904206206880156*^9, 
   3.904206225512169*^9}, {3.904206809443274*^9, 3.904206827348784*^9}, {
   3.904206911738882*^9, 3.904206944727193*^9}, {3.904207045168487*^9, 
   3.904207074789702*^9}, {3.905886833675251*^9, 3.905886851530014*^9}, 
   3.9058873789094343`*^9, {3.917438058690793*^9, 3.917438074888043*^9}, {
   3.920740062544218*^9, 3.920740079452146*^9}, {3.920740125000074*^9, 
   3.920740136816533*^9}, {3.920970965582795*^9, 3.920971003521529*^9}, 
   3.920971069080553*^9, {3.9209717482968493`*^9, 3.9209717809378023`*^9}, {
   3.920973092787004*^9, 3.920973093277817*^9}, {3.9209767997549057`*^9, 
   3.920976799960064*^9}, {3.921045214284996*^9, 3.921045255482876*^9}, {
   3.921065545769367*^9, 3.9210655608698606`*^9}, {3.921324678072748*^9, 
   3.9213246810172*^9}, {3.9213497035930634`*^9, 3.921349777098077*^9}, {
   3.922643918320344*^9, 3.92264395870716*^9}, {3.922643992140358*^9, 
   3.92264399255906*^9}, {3.9226440623616543`*^9, 3.922644079377473*^9}, {
   3.922644118228977*^9, 3.9226441351607533`*^9}, {3.9226444824757633`*^9, 
   3.922644482708815*^9}, {3.922644548535943*^9, 3.922644550714991*^9}, {
   3.922644581714714*^9, 3.922644600099608*^9}, {3.922689563321958*^9, 
   3.922689589588739*^9}, {3.9226897533626328`*^9, 3.922689777777259*^9}, {
   3.9226901327768717`*^9, 3.922690164160863*^9}, {3.922690359827386*^9, 
   3.9226903845515423`*^9}, {3.922690920412593*^9, 3.9226909354794493`*^9}, {
   3.922691406126976*^9, 3.922691424433531*^9}, {3.922691590441113*^9, 
   3.922691610169632*^9}, {3.922691675506907*^9, 3.9226917060537043`*^9}, {
   3.9226919959869547`*^9, 3.922691998149242*^9}, {3.922692042586507*^9, 
   3.922692047766774*^9}, {3.922692182662541*^9, 3.922692196404538*^9}, {
   3.9226923052864*^9, 3.92269233421452*^9}, {3.922692443199905*^9, 
   3.922692443394875*^9}, {3.9226924832081623`*^9, 3.922692488240101*^9}, {
   3.9227362041316*^9, 3.922736217795773*^9}, {3.922736485300528*^9, 
   3.922736515378201*^9}, {3.922738782322857*^9, 3.9227388149792013`*^9}, {
   3.922739131574955*^9, 3.922739145396455*^9}, {3.92273969293178*^9, 
   3.922739694911791*^9}, {3.922739801719276*^9, 3.922739825019079*^9}, {
   3.9228851973605633`*^9, 3.922885213206121*^9}, {3.9228852583786297`*^9, 
   3.922885273587716*^9}, {3.9230737317141943`*^9, 3.9230737775579367`*^9}, {
   3.9230738102430153`*^9, 3.923073812851996*^9}, {3.923073878692436*^9, 
   3.923073878979043*^9}, {3.923073916925398*^9, 3.92307397895407*^9}, 
   3.923074030125605*^9, {3.923074082108037*^9, 3.923074100624195*^9}, {
   3.923504902894904*^9, 3.9235049685685883`*^9}, {3.92351008020932*^9, 
   3.9235101058012533`*^9}, {3.92351040133503*^9, 3.9235104273030643`*^9}, {
   3.923510525188055*^9, 3.9235105303269453`*^9}, {3.923513020081737*^9, 
   3.923513048133346*^9}, {3.923559074876461*^9, 3.923559117613393*^9}, {
   3.923559168305451*^9, 3.923559212953896*^9}, {3.923559262588378*^9, 
   3.923559262728483*^9}, {3.923559985956718*^9, 3.92355999829683*^9}, {
   3.9235600964357767`*^9, 3.923560124254444*^9}, {3.9235735185284233`*^9, 
   3.923573571466653*^9}, {3.923574028498754*^9, 3.923574052761923*^9}, {
   3.923574716202265*^9, 3.9235747168164*^9}, {3.923575280336916*^9, 
   3.923575317675764*^9}, {3.923575396975774*^9, 3.923575422254575*^9}, {
   3.9235781482615833`*^9, 3.923578177022559*^9}, {3.923578243074225*^9, 
   3.923578274238298*^9}, {3.923578468628831*^9, 3.923578468747032*^9}, {
   3.923586578474441*^9, 3.923586607267047*^9}, {3.9235866437902927`*^9, 
   3.9235866559265013`*^9}, {3.923587432264936*^9, 3.923587474108862*^9}, {
   3.923587519775196*^9, 3.9235875201205273`*^9}, {3.923587563035923*^9, 
   3.923587600620729*^9}, {3.923727051144929*^9, 3.9237271309831047`*^9}, {
   3.923741085723544*^9, 3.923741113340124*^9}, {3.923741156685309*^9, 
   3.9237411569262867`*^9}, {3.923764452632042*^9, 3.923764481435761*^9}, {
   3.923764696708887*^9, 3.923764715610429*^9}, {3.9237648403199787`*^9, 
   3.923764863787088*^9}, {3.9237649298349867`*^9, 3.923764951113659*^9}, {
   3.924013033426303*^9, 3.924013088708456*^9}, {3.924013143028483*^9, 
   3.924013143139468*^9}, {3.924013215995028*^9, 3.924013243694845*^9}, {
   3.924014671925606*^9, 3.924014672036207*^9}},
 CellLabel->"In[76]:=",ExpressionUUID->"e2716c79-cd33-4d18-b860-cab9a17fb27d"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{
  RowBox[{"omega", "[", 
   RowBox[{"m", ",", "\[Delta]"}], "]"}], "   ", 
  RowBox[{"(*", 
   RowBox[{"0.5000074673979464", "-", 
    RowBox[{"0.0016272487945164053", "im"}]}], "*)"}]}]], "Input",
 CellChangeTimes->{{3.830286569452341*^9, 3.8302865917630796`*^9}, {
   3.830286624504738*^9, 3.830286629540207*^9}, {3.90180161733075*^9, 
   3.901801636031354*^9}, {3.9018025470356483`*^9, 3.90180256974561*^9}, 
   3.904206090875677*^9, 3.904206233309506*^9, 3.904206801025178*^9, {
   3.904206928296254*^9, 3.904206933798353*^9}, {3.904207063780761*^9, 
   3.904207067690407*^9}, 3.905886843658696*^9, {3.9174380656299067`*^9, 
   3.917438082549211*^9}, 3.920740072904454*^9, 3.92097098644176*^9, 
   3.9209710758192368`*^9, {3.920971755484761*^9, 3.920971755990182*^9}, {
   3.920976805247924*^9, 3.920976806067589*^9}, 3.921045222635839*^9, 
   3.921065552854018*^9, {3.9213497106096888`*^9, 3.9213497374082327`*^9}, {
   3.922643931394767*^9, 3.922643932215777*^9}, 3.922644567940151*^9, 
   3.9226895763601294`*^9, 3.9226897634774017`*^9, 3.92269015231689*^9, 
   3.922690367693619*^9, 3.922690914368092*^9, 3.922691413297625*^9, 
   3.922691584544568*^9, 3.922691693976543*^9, 3.922692206016552*^9, {
   3.9226923212951193`*^9, 3.922692322481473*^9}, 3.922692477465674*^9, {
   3.922736194892755*^9, 3.9227361959108257`*^9}, 3.922736503874167*^9, {
   3.922738797790175*^9, 3.922738799002483*^9}, 3.9227391249592447`*^9, 
   3.9227398104343843`*^9, 3.922885220205887*^9, 3.922885267737486*^9, 
   3.9230737395911417`*^9, 3.923073901976902*^9, 3.923074022232801*^9, 
   3.9235049124644547`*^9, 3.923510408870891*^9, {3.923513027777852*^9, 
   3.923513028301552*^9}, 3.923559081653397*^9, 3.923559176454253*^9, 
   3.92355999257987*^9, 3.923560103214472*^9, {3.923573526047771*^9, 
   3.923573531003495*^9}, {3.9235740150253*^9, 3.923574019632907*^9}, 
   3.923575287666127*^9, 3.923575389878745*^9, 3.923578161660923*^9, 
   3.923578249045199*^9, 3.923586586212883*^9, 3.923587426515493*^9, 
   3.92358757077067*^9, 3.9237270441122437`*^9, 3.923741099191023*^9, 
   3.923764468885539*^9, 3.923764705502458*^9, {3.923764846926652*^9, 
   3.9237648519918756`*^9}, {3.923764935930066*^9, 3.923764940207077*^9}, 
   3.9240130429625893`*^9, 3.924013222131042*^9, {3.938446070527566*^9, 
   3.938446071431932*^9}},
 CellLabel->"In[81]:=",ExpressionUUID->"f8be4107-5ff0-448c-9446-6d1a735a187e"],

Cell[BoxData[
 RowBox[{"0.5000001442047644`", "\[VeryThinSpace]", "-", 
  RowBox[{"0.0016272121604910279`", " ", "\[ImaginaryI]"}]}]], "Output",
 CellChangeTimes->{
  3.923764871757906*^9, 3.923764954030159*^9, {3.9240130591972446`*^9, 
   3.924013092504571*^9}, 3.924013145534088*^9, {3.9240132357327967`*^9, 
   3.924013245998685*^9}, {3.924014665990562*^9, 3.924014675334713*^9}, 
   3.938446073090933*^9},
 CellLabel->"Out[81]=",ExpressionUUID->"f924778d-3c56-4b78-ae72-a8f787947c30"]
}, Open  ]]
}, Open  ]],

Cell[CellGroupData[{

Cell["Case \[Mu] = 0.3 and a/M = 0.999951495", "Subsubsection",
 CellChangeTimes->{{3.9042067561650257`*^9, 3.904206756784425*^9}, {
   3.904206869414483*^9, 3.904206870821731*^9}, {3.904207047496584*^9, 
   3.904207048734906*^9}, {3.912236453516667*^9, 3.912236462654896*^9}, {
   3.921316885650226*^9, 3.921316885771687*^9}, 
   3.938446117549721*^9},ExpressionUUID->"49220381-15f7-4d86-8a8f-\
626b426c70ef"],

Cell[BoxData[
 RowBox[{"Clear", "[", 
  RowBox[{"\[Mu]", ",", "\[Lambda]0"}], "]"}]], "Input",
 CellChangeTimes->{{3.904205995592761*^9, 3.904206004152631*^9}},
 CellLabel->"In[89]:=",ExpressionUUID->"5ca54101-30c9-49ef-88a9-fbcae216bb61"],

Cell[BoxData[{
 RowBox[{"\[Lambda]0", ":=", 
  RowBox[{"1.967785116471808", "+", 
   RowBox[{
   "0.0022339894294982894", "\[ImaginaryI]"}]}]}], "\[IndentingNewLine]", 
 RowBox[{"m", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"n", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"\[Delta]", ":=", 
  RowBox[{"ArcCos", "[", "0.999951495", "]"}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Mu]", ":=", "0.3"}]}], "Input",
 CellChangeTimes->{{3.8302854864701757`*^9, 3.830285491653075*^9}, {
   3.901801477300082*^9, 3.9018015543030567`*^9}, {3.9018024780612373`*^9, 
   3.901802483901363*^9}, {3.904206024457182*^9, 3.904206027582178*^9}, {
   3.904206059414187*^9, 3.904206070972438*^9}, {3.904206206880156*^9, 
   3.904206225512169*^9}, {3.904206809443274*^9, 3.904206827348784*^9}, {
   3.904206911738882*^9, 3.904206944727193*^9}, {3.904207045168487*^9, 
   3.904207074789702*^9}, {3.905886833675251*^9, 3.905886851530014*^9}, 
   3.9058873789094343`*^9, {3.917438058690793*^9, 3.917438074888043*^9}, {
   3.91950044872159*^9, 3.919500478496819*^9}, {3.9195006283985977`*^9, 
   3.9195006307097683`*^9}, {3.92131672410885*^9, 3.9213167919357843`*^9}, {
   3.92131689630324*^9, 3.92131692892467*^9}, {3.9219242912683268`*^9, 
   3.9219243539054203`*^9}, {3.921944558231051*^9, 3.9219445706901827`*^9}, {
   3.9219446136048203`*^9, 3.921944613800376*^9}, {3.921944744400111*^9, 
   3.9219448000664043`*^9}, {3.921959264243073*^9, 3.921959284005199*^9}, {
   3.938445709918769*^9, 3.938445710141603*^9}, 3.938445740667251*^9},
 CellLabel->"In[90]:=",ExpressionUUID->"7d09d09e-3ea7-42ad-a8d0-7aed25b991ff"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{
  RowBox[{"omega", "[", 
   RowBox[{"m", ",", " ", "\[Delta]"}], "]"}], "   ", 
  RowBox[{"(*", 
   RowBox[{"0.5006238781038775", "-", 
    RowBox[{"0.011075332554396777", "im"}]}], "*)"}]}]], "Input",
 CellChangeTimes->{{3.830286569452341*^9, 3.8302865917630796`*^9}, {
   3.830286624504738*^9, 3.830286629540207*^9}, {3.90180161733075*^9, 
   3.901801636031354*^9}, {3.9018025470356483`*^9, 3.90180256974561*^9}, 
   3.904206090875677*^9, 3.904206233309506*^9, 3.904206801025178*^9, {
   3.904206928296254*^9, 3.904206933798353*^9}, {3.904207063780761*^9, 
   3.904207067690407*^9}, 3.905886843658696*^9, {3.9174380656299067`*^9, 
   3.917438082549211*^9}, 3.919500459288505*^9, 3.919500622009674*^9, {
   3.921316731365829*^9, 3.921316757743691*^9}, 3.921316902054076*^9, 
   3.921924310767272*^9, 3.9219445643852987`*^9, 3.921944753610352*^9, 
   3.9219592712392607`*^9, {3.938446096311534*^9, 3.938446104978162*^9}},
 CellLabel->"In[97]:=",ExpressionUUID->"4688f918-f856-4a90-8c9b-26fc11f61d19"],

Cell[BoxData[
 RowBox[{"0.500007365407821`", "\[VeryThinSpace]", "-", 
  RowBox[{"0.011065076134461303`", " ", "\[ImaginaryI]"}]}]], "Output",
 CellChangeTimes->{
  3.830286612202793*^9, {3.901801620568372*^9, 3.9018016365843973`*^9}, 
   3.901802249972011*^9, 3.90180250590245*^9, {3.901802542893785*^9, 
   3.90180254866963*^9}, 3.904205543002056*^9, 3.9042060798201523`*^9, 
   3.90420624543332*^9, {3.904206819435219*^9, 3.9042068334420424`*^9}, 
   3.904206952379912*^9, 3.904207082572421*^9, 3.9174380831683598`*^9, 
   3.917585726055129*^9, 3.9195004923452673`*^9, 3.919500640175137*^9, 
   3.921316778550865*^9, {3.921316920184292*^9, 3.921316931233438*^9}, 
   3.9213177064119387`*^9, {3.921924336888233*^9, 3.921924355950315*^9}, 
   3.921944576911003*^9, 3.9219446179674683`*^9, {3.9219447696171017`*^9, 
   3.921944802682652*^9}, 3.921959286513595*^9, {3.938446098054955*^9, 
   3.938446105437871*^9}},
 CellLabel->"Out[97]=",ExpressionUUID->"534a7c3e-5575-4a09-9a3b-1b4ccd174578"]
}, Open  ]]
}, Open  ]],

Cell[CellGroupData[{

Cell["Case \[Mu] = 0.4 and a/M = 0.999999", "Subsubsection",
 CellChangeTimes->{{3.9042067561650257`*^9, 3.904206756784425*^9}, {
  3.904206869414483*^9, 3.904206870821731*^9}, {3.904207047496584*^9, 
  3.904207048734906*^9}, {3.912236453516667*^9, 3.912236462654896*^9}, {
  3.921316885650226*^9, 3.921316885771687*^9}, {3.9213177213172817`*^9, 
  3.921317721680867*^9}, {3.9384461391275663`*^9, 
  3.938446141238928*^9}},ExpressionUUID->"7b368b51-a6ff-4564-b8b7-\
503f1ccfa3cc"],

Cell[BoxData[
 RowBox[{"Clear", "[", 
  RowBox[{"\[Mu]", ",", "\[Lambda]0"}], "]"}]], "Input",
 CellChangeTimes->{{3.904205995592761*^9, 3.904206004152631*^9}},
 CellLabel->"In[87]:=",ExpressionUUID->"0b92e8dc-a4a3-4fe9-b67e-09951b17dc63"],

Cell[BoxData[{
 RowBox[{"\[Lambda]0", ":=", 
  RowBox[{"1.981962300315113", "+", 
   RowBox[{
   "0.0001847814130993125", "\[ImaginaryI]"}]}]}], "\[IndentingNewLine]", 
 RowBox[{"m", ":=", "1"}], "\[IndentingNewLine]", 
 RowBox[{"n", ":=", "0"}], "\[IndentingNewLine]", 
 RowBox[{"\[Delta]", ":=", 
  RowBox[{"ArcCos", "[", "0.999999", "]"}]}], "\[IndentingNewLine]", 
 RowBox[{"\[Mu]", ":=", "0.4"}]}], "Input",
 CellChangeTimes->{{3.8302854864701757`*^9, 3.830285491653075*^9}, {
   3.901801477300082*^9, 3.9018015543030567`*^9}, {3.9018024780612373`*^9, 
   3.901802483901363*^9}, {3.904206024457182*^9, 3.904206027582178*^9}, {
   3.904206059414187*^9, 3.904206070972438*^9}, {3.904206206880156*^9, 
   3.904206225512169*^9}, {3.904206809443274*^9, 3.904206827348784*^9}, {
   3.904206911738882*^9, 3.904206944727193*^9}, {3.904207045168487*^9, 
   3.904207074789702*^9}, {3.905886833675251*^9, 3.905886851530014*^9}, 
   3.9058873789094343`*^9, {3.917438058690793*^9, 3.917438074888043*^9}, {
   3.91950044872159*^9, 3.919500478496819*^9}, {3.9195006283985977`*^9, 
   3.9195006307097683`*^9}, {3.92131672410885*^9, 3.9213167919357843`*^9}, {
   3.92131689630324*^9, 3.92131692892467*^9}, {3.921317726170247*^9, 
   3.921317726435561*^9}, {3.921317788029991*^9, 3.921317828441983*^9}, {
   3.921322506632887*^9, 3.921322571095107*^9}, {3.9213277033938713`*^9, 
   3.921327757891774*^9}, {3.921327866772736*^9, 3.921327873733233*^9}, {
   3.921328419650489*^9, 3.92132841998302*^9}, {3.921347585713442*^9, 
   3.921347619533103*^9}, {3.921349110831326*^9, 3.921349118881369*^9}},
 CellLabel->"In[98]:=",ExpressionUUID->"1a552bd3-f6c8-49c5-8dc1-439cb53b2e42"],

Cell[CellGroupData[{

Cell[BoxData[
 RowBox[{
  RowBox[{"omega", "[", 
   RowBox[{"m", ",", " ", "\[Delta]"}], "]"}], "   ", 
  RowBox[{"(*", 
   RowBox[{"0.5000039445554805", "-", 
    RowBox[{"0.000920102281519886", "im"}]}], "*)"}]}]], "Input",
 CellChangeTimes->{{3.830286569452341*^9, 3.8302865917630796`*^9}, {
   3.830286624504738*^9, 3.830286629540207*^9}, {3.90180161733075*^9, 
   3.901801636031354*^9}, {3.9018025470356483`*^9, 3.90180256974561*^9}, 
   3.904206090875677*^9, 3.904206233309506*^9, 3.904206801025178*^9, {
   3.904206928296254*^9, 3.904206933798353*^9}, {3.904207063780761*^9, 
   3.904207067690407*^9}, 3.905886843658696*^9, {3.9174380656299067`*^9, 
   3.917438082549211*^9}, 3.919500459288505*^9, 3.919500622009674*^9, {
   3.921316731365829*^9, 3.921316757743691*^9}, 3.921316902054076*^9, 
   3.92131780072195*^9, 3.921322513560306*^9, {3.92132772341352*^9, 
   3.9213277477722483`*^9}, 3.921349103911397*^9, {3.938446125429944*^9, 
   3.938446125928863*^9}},
 CellLabel->
  "In[103]:=",ExpressionUUID->"e972fb3d-4fc9-46d8-84d6-40705f6f4b90"],

Cell[BoxData[
 RowBox[{"0.5000000815377194`", "\[VeryThinSpace]", "-", 
  RowBox[{"0.0009201054567815688`", " ", "\[ImaginaryI]"}]}]], "Output",
 CellChangeTimes->{
  3.830286612202793*^9, {3.901801620568372*^9, 3.9018016365843973`*^9}, 
   3.901802249972011*^9, 3.90180250590245*^9, {3.901802542893785*^9, 
   3.90180254866963*^9}, 3.904205543002056*^9, 3.9042060798201523`*^9, 
   3.90420624543332*^9, {3.904206819435219*^9, 3.9042068334420424`*^9}, 
   3.904206952379912*^9, 3.904207082572421*^9, 3.9174380831683598`*^9, 
   3.917585726055129*^9, 3.9195004923452673`*^9, 3.919500640175137*^9, 
   3.921316778550865*^9, {3.921316920184292*^9, 3.921316931233438*^9}, 
   3.9213177064119387`*^9, {3.921317808461397*^9, 3.92131783065963*^9}, {
   3.921322522652074*^9, 3.921322574189073*^9}, {3.921327742744296*^9, 
   3.92132776393629*^9}, {3.921327870954198*^9, 3.921327879364101*^9}, 
   3.921328422858674*^9, {3.9213475886361628`*^9, 3.921347621703315*^9}, 
   3.921349129901661*^9, 3.938446126660439*^9},
 CellLabel->
  "Out[103]=",ExpressionUUID->"c9abfcab-e3aa-40d3-9498-2e678f517f0a"]
}, Open  ]]
}, Open  ]]
}, Open  ]]
},
WindowSize->{1440, 847},
WindowMargins->{{0, Automatic}, {Automatic, 0}},
TaggingRules->{
 "WelcomeScreenSettings" -> {"FEStarting" -> False}, "TryRealOnly" -> False},
Magnification:>1.5 Inherited,
FrontEndVersion->"13.1 for Mac OS X ARM (64-bit) (June 16, 2022)",
StyleDefinitions->"Default.nb",
ExpressionUUID->"b9b93df6-d5ff-49e9-97b0-b7bbff2940fb"
]
(* End of Notebook Content *)

(* Internal cache information *)
(*CellTagsOutline
CellTagsIndex->{}
*)
(*CellTagsIndex
CellTagsIndex->{}
*)
(*NotebookFileOutline
Notebook[{
Cell[558, 20, 3253, 85, 133, "Input",ExpressionUUID->"3c354bc7-bab1-421e-9d1b-5127297eddd2"],
Cell[CellGroupData[{
Cell[3836, 109, 231, 4, 67, "Subsubsection",ExpressionUUID->"e3bc185e-a009-4831-b51c-0b71b7ed0db5"],
Cell[4070, 115, 1180, 18, 157, "Text",ExpressionUUID->"5424cdb5-60be-413f-8837-795a40332f24"],
Cell[5253, 135, 13714, 366, 741, "Input",ExpressionUUID->"51e5da15-cdc7-48c3-964b-75d035f9ff8f"],
Cell[18970, 503, 1622, 41, 233, "Input",ExpressionUUID->"897eb352-1395-4082-adab-ddfcfde5df8a"],
Cell[CellGroupData[{
Cell[20617, 548, 684, 15, 66, "Input",ExpressionUUID->"a71b394d-ccf4-4db7-95d4-3d03782358ab"],
Cell[21304, 565, 1698, 45, 84, "Output",ExpressionUUID->"289f3318-79fa-4fee-ae20-3b71d9918971"]
}, Open  ]],
Cell[23017, 613, 1187, 37, 84, "Input",ExpressionUUID->"cf66def5-6c3a-466a-9f3d-875351881f70"]
}, Open  ]],
Cell[CellGroupData[{
Cell[24241, 655, 426, 6, 79, "Subsection",ExpressionUUID->"6dfc9c28-0cab-4333-8390-d08e6e0d4568"],
Cell[24670, 663, 1101, 17, 88, "Text",ExpressionUUID->"0e781e5e-e66d-482b-b84e-ee4ed49b4500"],
Cell[25774, 682, 3663, 106, 367, "Input",ExpressionUUID->"42249c9b-6279-4a00-8062-dc3cd9b9a8c6"],
Cell[CellGroupData[{
Cell[29462, 792, 399, 6, 67, "Subsubsection",ExpressionUUID->"bada998a-328e-4ace-a050-878085cac934"],
Cell[29864, 800, 1178, 18, 88, "Text",ExpressionUUID->"775fc218-d170-4800-8c69-5f54d93cbbc9"],
Cell[31045, 820, 791, 14, 46, "Input",ExpressionUUID->"8daca6b0-7194-424b-a651-dfbde5712bed"],
Cell[31839, 836, 1649, 27, 78, "Input",ExpressionUUID->"cfa24386-7085-45ce-8ebf-7fbdaa0c2b2e"]
}, Open  ]],
Cell[CellGroupData[{
Cell[33525, 868, 332, 6, 67, "Subsubsection",ExpressionUUID->"7faf3c8c-c0bf-49a7-a918-bcdc114f28c5"],
Cell[33860, 876, 8168, 198, 345, "Input",ExpressionUUID->"a4802582-eafa-410b-99cf-cae768b02ffc"],
Cell[42031, 1076, 2261, 45, 69, "Input",ExpressionUUID->"8b04c223-3423-470a-9a8b-e05cf160d71c"],
Cell[CellGroupData[{
Cell[44317, 1125, 1791, 39, 88, "Input",ExpressionUUID->"1a1f763e-d709-4ca2-9266-2ce530b6bc14"],
Cell[46111, 1166, 1699, 47, 105, "Output",ExpressionUUID->"345147c4-2773-438d-9383-187fb6653ea9"]
}, Open  ]]
}, Open  ]],
Cell[CellGroupData[{
Cell[47859, 1219, 2045, 37, 107, "Subsubsection",ExpressionUUID->"bc039293-5f6e-476d-adc3-19c387be8b25"],
Cell[49907, 1258, 1418, 25, 51, "Input",ExpressionUUID->"169d079e-1cb6-41e9-b6d1-46390addb01a"]
}, Open  ]],
Cell[CellGroupData[{
Cell[51362, 1288, 1149, 16, 67, "Subsubsection",ExpressionUUID->"52fbabbe-fcac-4fbe-a9af-2c0ac8bc9648"],
Cell[52514, 1306, 1899, 53, 325, "Input",ExpressionUUID->"bf8d7159-fe84-44a4-a13c-edd220e1c70c"]
}, Open  ]],
Cell[CellGroupData[{
Cell[54450, 1364, 353, 5, 67, "Subsubsection",ExpressionUUID->"be025626-ed0a-4b3f-8e64-3b4b7fc69e68"],
Cell[54806, 1371, 1745, 52, 247, "Input",ExpressionUUID->"42ffa095-96aa-4490-a111-0d2205b1c555"],
Cell[CellGroupData[{
Cell[56576, 1427, 381, 8, 66, "Input",ExpressionUUID->"ee27f6b3-cf07-4fb7-8581-1fa861027255"],
Cell[56960, 1437, 3657, 103, 161, "Output",ExpressionUUID->"473b99a2-6b6d-4176-a234-e42568c67326"]
}, Open  ]]
}, Open  ]],
Cell[CellGroupData[{
Cell[60666, 1546, 374, 6, 67, "Subsubsection",ExpressionUUID->"28d255c2-f38d-4945-a5c2-46e3a82ac408"],
Cell[61043, 1554, 1923, 59, 168, "Input",ExpressionUUID->"bab07929-36a8-45ca-9d03-e622ebb1f052"],
Cell[CellGroupData[{
Cell[62991, 1617, 478, 11, 66, "Input",ExpressionUUID->"540fb893-f25f-4be7-af83-50edf2628b01"],
Cell[63472, 1630, 3998, 110, 132, "Output",ExpressionUUID->"0c95c917-e6cd-4720-a634-b2a97e7485a8"]
}, Open  ]],
Cell[67485, 1743, 1249, 19, 53, "Text",ExpressionUUID->"158edd35-cecc-4132-a4d3-66c223c62325"],
Cell[68737, 1764, 2698, 82, 271, "Input",ExpressionUUID->"7cb2ff18-6aec-4760-86f6-cb234b04b8ba"],
Cell[71438, 1848, 2262, 45, 88, "Text",ExpressionUUID->"c8f0dfbb-dd4f-411d-8797-2e460dcca9a3"],
Cell[73703, 1895, 2629, 82, 301, "Input",ExpressionUUID->"0349a5e1-090d-4449-9b48-cb465cab1f8a"],
Cell[76335, 1979, 1825, 33, 94, "Text",ExpressionUUID->"137ef649-c99b-4e67-954a-7d21d8e68a9c"],
Cell[78163, 2014, 1074, 26, 68, "Input",ExpressionUUID->"8e4982fe-867d-40b7-ba51-42db32dd4318"],
Cell[79240, 2042, 599, 9, 106, "Text",ExpressionUUID->"2f799c9f-c272-48a1-8c6b-b46754589bf9"],
Cell[79842, 2053, 525, 12, 58, "Input",ExpressionUUID->"7e3f0040-d93e-4b9f-aaef-52ae7d92080c"],
Cell[80370, 2067, 1431, 23, 194, "Input",ExpressionUUID->"f82295ee-**************-51f0eaa0fd6f"],
Cell[CellGroupData[{
Cell[81826, 2094, 855, 15, 46, "Input",ExpressionUUID->"997d6ce4-1709-4273-8601-13a71fe79aa9"],
Cell[82684, 2111, 414, 7, 52, "Output",ExpressionUUID->"17e91640-4942-47ce-b432-d9ed4536362c"]
}, Open  ]],
Cell[83113, 2121, 172, 3, 66, "Input",ExpressionUUID->"d120976b-2849-4a15-a45a-b04c27563d5a"],
Cell[83288, 2126, 476, 11, 58, "Input",ExpressionUUID->"54e922f5-4b5c-4a0a-82d6-7995e2d3e3f2"],
Cell[83767, 2139, 1258, 21, 163, "Input",ExpressionUUID->"700458f0-9b0b-4af0-9533-42fe1e54f279"],
Cell[CellGroupData[{
Cell[85050, 2164, 833, 15, 46, "Input",ExpressionUUID->"2d0ee3c0-d989-4ae9-be18-8445e2250d77"],
Cell[85886, 2181, 313, 5, 52, "Output",ExpressionUUID->"86f6df01-c2ab-4b42-a08a-a6340f3f7cc7"]
}, Open  ]],
Cell[86214, 2189, 480, 12, 57, "Input",ExpressionUUID->"05ba4f1e-87cb-40c5-b4b2-75db4d06924e"],
Cell[86697, 2203, 288, 5, 66, "Input",ExpressionUUID->"d042a024-845e-42e0-a998-9a11525a0799"],
Cell[86988, 2210, 1278, 21, 194, "Input",ExpressionUUID->"e4fc09e7-f2cb-4874-b92f-50f349a993ca"],
Cell[CellGroupData[{
Cell[88291, 2235, 686, 13, 46, "Input",ExpressionUUID->"1bccd56e-f95c-4367-a562-ba6a37fcc104"],
Cell[88980, 2250, 410, 6, 52, "Output",ExpressionUUID->"8ef20ec9-2878-4988-94a0-ea1a4b4fdc01"]
}, Open  ]],
Cell[89405, 2259, 456, 12, 57, "Input",ExpressionUUID->"7b23a06d-e37b-478e-983c-dc10c1da9f9e"],
Cell[89864, 2273, 239, 4, 66, "Input",ExpressionUUID->"1fe9f47b-1814-47d6-8363-792bcf6bcd94"],
Cell[90106, 2279, 1179, 21, 194, "Input",ExpressionUUID->"4c96ddf8-af91-4a7a-92ce-e73294147a29"],
Cell[CellGroupData[{
Cell[91310, 2304, 731, 13, 46, "Input",ExpressionUUID->"9e57bb30-02bb-4c5e-b41d-529963db6730"],
Cell[92044, 2319, 268, 4, 52, "Output",ExpressionUUID->"057de5a9-e5fd-4fd7-ad29-31c131d26c9c"]
}, Open  ]],
Cell[92327, 2326, 464, 12, 57, "Input",ExpressionUUID->"caaf1bde-89c1-454e-b96f-9053d61a4167"],
Cell[92794, 2340, 239, 4, 66, "Input",ExpressionUUID->"ad0c46db-4131-4e48-ab35-a7cb134af1c1"],
Cell[93036, 2346, 1484, 24, 163, "Input",ExpressionUUID->"c627da7e-c1c4-46bb-ada9-3c0a918bc154"],
Cell[CellGroupData[{
Cell[94545, 2374, 971, 17, 46, "Input",ExpressionUUID->"24b79b7f-fc13-4852-925c-95771d8d27a1"],
Cell[95519, 2393, 289, 4, 52, "Output",ExpressionUUID->"cd4c7c48-c692-4fc3-aa77-c818bf867e32"]
}, Open  ]],
Cell[95823, 2400, 509, 12, 57, "Input",ExpressionUUID->"2af8fdf2-b255-41da-830d-66f207295279"],
Cell[96335, 2414, 239, 4, 66, "Input",ExpressionUUID->"9cd33f22-5c11-4e40-bf46-ac0bd7309225"],
Cell[96577, 2420, 1121, 19, 163, "Input",ExpressionUUID->"959453d1-08d8-4cef-baf7-eb5de6c4aceb"],
Cell[CellGroupData[{
Cell[97723, 2443, 808, 14, 46, "Input",ExpressionUUID->"f2278f0b-87a6-4f9c-acee-6c582f16b340"],
Cell[98534, 2459, 288, 4, 52, "Output",ExpressionUUID->"315bd5cd-07a3-4fad-96b9-c006cf0ba58e"]
}, Open  ]],
Cell[98837, 2466, 561, 13, 57, "Input",ExpressionUUID->"55ec6685-a988-4287-9f96-1ea655b61783"],
Cell[99401, 2481, 239, 4, 66, "Input",ExpressionUUID->"898b807a-e51e-4965-b6dd-cddf6e6d003c"],
Cell[99643, 2487, 1197, 20, 163, "Input",ExpressionUUID->"aaaa8e85-8775-47a5-95b1-77597699cddd"],
Cell[CellGroupData[{
Cell[100865, 2511, 852, 15, 46, "Input",ExpressionUUID->"159ff6ec-8600-4c22-ae8b-f1a60408818d"],
Cell[101720, 2528, 267, 4, 52, "Output",ExpressionUUID->"dcdc6de5-ad57-4d11-9a64-b3c44b147efb"]
}, Open  ]]
}, Open  ]],
Cell[CellGroupData[{
Cell[102036, 2538, 510, 7, 67, "Subsubsection",ExpressionUUID->"fac1d365-d4b7-42f4-a177-e64491fe2d02"],
Cell[102549, 2547, 239, 4, 66, "Input",ExpressionUUID->"8da987ca-2f13-441d-b1e3-278252b0eac6"],
Cell[102791, 2553, 4975, 71, 194, "Input",ExpressionUUID->"e2716c79-cd33-4d18-b860-cab9a17fb27d"],
Cell[CellGroupData[{
Cell[107791, 2628, 2416, 37, 46, "Input",ExpressionUUID->"f8be4107-5ff0-448c-9446-6d1a735a187e"],
Cell[110210, 2667, 488, 8, 52, "Output",ExpressionUUID->"f924778d-3c56-4b78-ae72-a8f787947c30"]
}, Open  ]]
}, Open  ]],
Cell[CellGroupData[{
Cell[110747, 2681, 410, 6, 67, "Subsubsection",ExpressionUUID->"49220381-15f7-4d86-8a8f-626b426c70ef"],
Cell[111160, 2689, 239, 4, 66, "Input",ExpressionUUID->"5ca54101-30c9-49ef-88a9-fbcae216bb61"],
Cell[111402, 2695, 1600, 25, 194, "Input",ExpressionUUID->"7d09d09e-3ea7-42ad-a8d0-7aed25b991ff"],
Cell[CellGroupData[{
Cell[113027, 2724, 1024, 17, 46, "Input",ExpressionUUID->"4688f918-f856-4a90-8c9b-26fc11f61d19"],
Cell[114054, 2743, 993, 15, 52, "Output",ExpressionUUID->"534a7c3e-5575-4a09-9a3b-1b4ccd174578"]
}, Open  ]]
}, Open  ]],
Cell[CellGroupData[{
Cell[115096, 2764, 480, 7, 67, "Subsubsection",ExpressionUUID->"7b368b51-a6ff-4564-b8b7-503f1ccfa3cc"],
Cell[115579, 2773, 239, 4, 66, "Input",ExpressionUUID->"0b92e8dc-a4a3-4fe9-b67e-09951b17dc63"],
Cell[115821, 2779, 1662, 26, 194, "Input",ExpressionUUID->"1a552bd3-f6c8-49c5-8dc1-439cb53b2e42"],
Cell[CellGroupData[{
Cell[117508, 2809, 1052, 19, 46, "Input",ExpressionUUID->"e972fb3d-4fc9-46d8-84d6-40705f6f4b90"],
Cell[118563, 2830, 1091, 17, 52, "Output",ExpressionUUID->"c9abfcab-e3aa-40d3-9498-2e678f517f0a"]
}, Open  ]]
}, Open  ]]
}, Open  ]]
}
]
*)

