#!/usr/bin/env julia
# -*- coding: utf-8 -*-
using ArbNumerics
using LinearAlgebra
using PyPlot
using PyCall

setprecision(ArbComplex,digits=512)

# Parameters

spin = ArbReal(0)        # negative for outgoing fields
angularl = ArbReal(1)
angularm = ArbReal(1)

Nc = 128        # fixes level for convergents
Nf = 64         # fixes level for Fredholm expansion expansion
MAXSTEPS =100	# fixes max no of steps for root-finding																																																																																																																																																								 

# Compute the tau for PV using the Fredholm determinant expansion

function pieV(th,sig)
    return (ArbNumerics.gamma(1-sig)^2*ArbNumerics.rgamma(1+sig)^2 *
            ArbNumerics.gamma(1+(th[3]+sig)/2)*ArbNumerics.rgamma(1+(th[3]-sig)/2) *
            ArbNumerics.gamma(1+(th[2]+th[1]+sig)/2)*ArbNumerics.rgamma(1+(th[2]+th[1]-sig)/2) *
            ArbNumerics.gamma(1+(th[2]-th[1]+sig)/2)*ArbNumerics.rgamma(1+(th[2]-th[1]-sig)/2))
end

function invertseries(seq)
    result = zeros(ArbComplex,2,2,Nf+1)
    result[:,:,1] = [ 1 0; 0 1 ]
    for p = 2:Nf+1
        A = -seq[:,:,p]
        for q = 2:p-1
             A += -(seq[:,:,q]*result[:,:,p-q+1])
        end
        result[:,:,p] = A
    end
    return result
end

function gee(th1,th2,th3)
    psi = zeros(ArbComplex,2,2,Nf+1)
    a = (th1-th2+th3)/2
    b = (th1-th2-th3)/2
    c = th1
    # psi is actually invariant by conjugation by a diagonal matrix
    psi[:,:,1] = [ 1 0 ; 0 1 ]
    psi[:,:,2] = [ (a*b/c) (-a*b/c/(1+c)) ; ((a-c)*(b-c)/c/(1-c)) (-(a-c)*(b-c)/c) ]
    for p = 3:Nf+1
        psi[1,1,p] = ((a+p-2)*(b+p-2)/((c+p-2)*(p-1))*psi[1,1,p-1])
        psi[1,2,p] = ((a+p-2)*(b+p-2)/((c+p-1)*(p-2))*psi[1,2,p-1]) ;
        psi[2,1,p] = ((a-c+p-2)*(b-c+p-2)/((-c+p-1)*(p-2))*psi[2,1,p-1])
        psi[2,2,p] = ((a-c+p-2)*(b-c+p-2)/((-c+p-2)*(p-1))*psi[2,2,p-1])
    end
    return psi
end

function geeV(th1,th2,t0=1.0)
    psi = zeros(ArbComplex,2,2,Nf+1)
    a = (th1-th2)/2
    c = th1
    psi[:,:,1] = [ 1 0 ; 0 1 ]
    psi[:,:,2] = [ (a/c*t0) (-a/(c*(1+c))*t0) ; ((a-c)/(c*(1-c))*t0) (-(a-c)/c*t0) ]
    for p = 3:Nf+1
        psi[1,1,p] = ((a+p-2)/((c+p-2)*(p-1))*psi[1,1,p-1]*t0)
        psi[1,2,p] = ((a+p-2)/((c+p-1)*(p-2))*psi[1,2,p-1]*t0)
        psi[2,1,p] = ((a-c+p-2)/((-c+p-1)*(p-2))*psi[2,1,p-1]*t0)
        psi[2,2,p] = ((a-c+p-2)/((-c+p-2)*(p-1))*psi[2,2,p-1]*t0)
    end
    return psi
end

function BuildA(sig,th1,th2)
    vecg = gee(sig,th1,th2)
    vecginv = invertseries(vecg)
    bigA = ArbNumerics.ArbComplexMatrix(zeros(ArbComplex,2Nf,2Nf))
    for p = 1:Nf
        for q = 1:Nf
            result = zeros(ArbComplex,2,2)
            if q + p <= Nf+1
                for r = 1:q
                    result += vecg[:,:,p+r]*vecginv[:,:,q-r+1]
                end
            end
            bigA[2*p-1:2*p,2*q-1:2*q] = result
        end
    end
    return bigA
end

function BuildD(sig,ths,x,t0)
    vecg = geeV(sig,ths,t0)
    vecginv = invertseries(vecg)
    bigD = ArbNumerics.ArbComplexMatrix(zeros(ArbComplex,2Nf,2Nf))
    left = [ (1/x) 0 ; 0 1 ]
    right = [ (x) 0 ; 0 1 ]
    for p = 1:Nf
        for q = 1:Nf
            result = zeros(ArbComplex,2,2)
            if q + p <= Nf+1
                for r = 1:p
                    result += -vecg[:,:,p-r+1]*vecginv[:,:,q+r]
                end
            end
            bigD[2*p-1:2*p,2*q-1:2*q] = left*result*right
        end
    end
    return bigD
end

function BuildDdiff(sig,ths,x,t)
    vecg = geeV(sig,ths,t)
    vecginv = invertseries(vecg)
    bigD = ArbNumerics.ArbComplexMatrix(zeros(ArbComplex,2Nf,2Nf))
    left = [ (1/x) 0 ; 0 1 ]
    right = [ (x) 0 ; 0 1 ]
    sigma3 = [ 1 0 ; 0 -1 ]
    for p = 1:Nf
        for q = 1:Nf
            result = zeros(ArbComplex,2,2)
            if q + p <= Nf+1
                for r = 1:p
                    result += -vecg[:,:,p-r+1]*vecginv[:,:,q+r]
                end
            end
            result = (p+q-1)/t*result+sig/(2t)*(sigma3*result-result*sigma3)
            bigD[(2p-1):(2p), (2q-1):(2q)] = left*result*right
            end
    end
    return bigD
end

function tauhat(th,sig,esse,t)
    x = esse*pieV(th,sig)*t^sig
    # theta[1] = theta1, theta[2] = thetainfinity, theta[3] = thetastar
    OPA = BuildA(sig,th[2],th[1])
    OPD = BuildD(-sig,th[3],x,-t)
    id = Matrix{ArbComplex}(I,2Nf,2Nf)
    Fred = ArbNumerics.ArbComplexMatrix(id - OPA*OPD)
    return ArbNumerics.determinant(Fred)
end

function firstlogdiffV(th,sig,esse,t)
    x = esse*pieV(th,sig)*t^sig
    OpA = BuildA(sig,th[2],th[1])
    OpD = BuildD(-sig,th[3],x,-t)
    OpDdiff = BuildDdiff(-sig,th[3],x,-t)
    Id = Matrix{ArbComplex}(I,2Nf,2Nf)
    OpKi = (ArbNumerics.ArbComplexMatrix(Id-OpA*OpD))^(-1)
    OpKdiff = ArbNumerics.ArbComplexMatrix(OpA*OpDdiff)
    return ArbNumerics.tr(ArbNumerics.ArbComplexMatrix(OpKdiff*OpKi))
end

function accessoryKV(th,sig,esse,t)
    theta = copy(th)
    theta[3] -= 1
    # Additional Schlesinger move th[2]=>th[2]-1,th[1]=>th[1]-1
    # prior to shift th[1]=>th[1]+1?
    theta[2] -= 1
    sig -= 1
    # esse is invariant
    prefactor = (sig^2-theta[3]^2)/(4t)+theta[2]
    return prefactor+firstlogdiffV(theta,sig,esse,t)
end

# Continuous Fraction method below:

function ei(th,sig,n)
    return (sig+th[3]+2*n-4)*(sig-th[3]-2*th[2]+2*n) +
           (2-th[3]-th[2])^2-th[1]^2
end

function bee(th,sig,n)
    return (sig+th[3]+2*n-2)*(sig-th[3]+2*n)
end

function see(th,sig,n)
    return 2*(sig+th[3]+2*n-2)
end

function dee(th,sig,n)
    return 2*(sig+th[3]+2*n)
end

function you0(th,sig,K,t)
    res = 0
    i = Nc
    while ( i > 0 )
        res = ei(th,sig,i)/(bee(th,sig,i)-4*K+t*(see(th,sig,i) -
              dee(th,sig,i)*res))
        i -= 1
    end
    return res
end

function vee0(th,sig,K,t)
    res = 0
    i = Nc
    while ( i > 0 )
        res = dee(th,sig,-i)/(bee(th,sig,-i)-4*K+t*(see(th,sig,-i) -
              ei(th,sig,-i)*res))
        i -= 1
    end
    return res
end

function equation(th,sig,K,t)
    return t*(ei(th,sig,0)*vee0(th,sig,K,t)-see(th,sig,0) +
           dee(th,sig,0)*you0(th,sig,K,t))+4*K-bee(th,sig,0)
end


function muller(f,x,verb,tol=1e-15,maxsteps=MAXSTEPS::Int64)
    h = ArbFloat(1e-8)

	local xv = [ ball(ArbComplex(x))[1] ball(ArbComplex(x+h))[1] ball(ArbComplex(x-h))[1] ]
	local fv = [ ball(ArbComplex(f(xv[1])))[1] ball(ArbComplex(f(xv[2])))[1] ball(ArbComplex(f(xv[3])))[1] ]
	local counter = 1

	while (counter < maxsteps) && (ball(real(abs(xv[1] - xv[2])))[1] > tol) && (ball(real(abs(fv[1])))[1] > tol )
        divdiff = [ ((fv[1]-fv[2])/(xv[1]-xv[2])) ((fv[1]-fv[3])/(xv[1]-xv[3])) ((fv[2]-fv[3])/(xv[2]-xv[3])) ]
        ddivdiff = (divdiff[1]-divdiff[3])/(xv[1]-xv[3])
        doubleu = divdiff[1]+divdiff[2]-divdiff[3]

        discr = sqrt(doubleu^2-4*fv[1]*ddivdiff)
        if (ball(real(abs(doubleu-discr)))[1] > (ball(real(abs(doubleu+discr)))[1]))
            xnew = xv[1]-2*fv[1]/(doubleu-discr)
        else
            xnew = xv[1]-2*fv[1]/(doubleu+discr)
        end
        xv = [ ball(ArbComplex(xnew))[1] xv[1] xv[2] ]
        fv = [ ball(ArbComplex(f(xnew)))[1] fv[1] fv[2] ]
        if verb
            println(counter," ",Complex{Float64}(xv[1])," ",Float64(abs(fv[1])))
        end
	    counter += 1
	end

	if counter >= maxsteps
	    error("Did not converge in ", string(maxsteps), " steps")
    else
	    xv[1], counter
    end
end

function abs2d(pt)
    return ArbFloat(ArbNumerics.sqrt(pt[1]*conj(pt[1])+pt[2]*conj(pt[2])))
end

function newton2d(f,x,verb,tol=1e-15,maxsteps=MAXSTEPS::Int64)
    h = ArbFloat(1e-8)

    local counter = 0

    xnew = transpose(x)
    while true
        local xv = zeros(ArbComplex,3,2)
        local fv = zeros(ArbComplex,3,2)
        xv[1,:] = xnew
        xv[2,:] = xnew + [ h 0 ]
        xv[3,:] = xnew + [ 0 h ]
        for i = 1:3
            fv[i,:] = f(xv[i,:])
        end
        jac = [ ((fv[2,1] - fv[1,1])/h) ((fv[3,1] - fv[1,1])/h) ; ((fv[2,2] - fv[1,2])/h) ((fv[3,2] - fv[1,2])/h) ]
        step = transpose(Array{ArbComplex,1}(inverse(ArbNumerics.ArbComplexMatrix(jac))*fv[1,:]))
        xnew = first.(ball.(xnew-step))
        counter += 1
        #println( counter," ",Complex{Float64}(xnew[1])," ",Complex{Float64}(xnew[2])," ",Float64(abs2d(fv[1,:])) )
        ((counter > maxsteps) || (abs2d(step) < tol) || (abs2d(fv[1,:]) < tol)) && break
    end

	if counter >= maxsteps
	    error("Did not converge in ", string(maxsteps), " steps")
    else
	    xnew, counter
    end
end


# Angular System: 

function angulareq(z,q)
    thang = zeros(ArbComplex,3)
    
    alpha =2*ArbNumerics.cos(eta)*ArbNumerics.sqrt(z^2 - mu^2)

    thang[1] = -angularm
    thang[2] = angularm
    thang[3] = 0
    sigang = (2.0*angularl+2.0)   # sigang -> -sigang for m<0
    tang = -2*alpha
    extrafac = (1-angularm)*alpha+(z^2-mu^2)*ArbNumerics.cos(eta)^2

    return equation(thang,sigang,q+extrafac,tang)
end

# radial system

function radialeq(z,q)
    thrad = zeros(ArbComplex,3)
    # Parameters for the radial equation:

    # z = M*omega
    # rplus/M = 1+sin(eta), rminus/M = 1-sin(eta)

    alpha = 2*ArbNumerics.sqrt(mu^2-z^2)

    thrad[1] = 2im*(1-ArbNumerics.sin(eta))/ArbNumerics.sin(eta)*z-1im*angularm*ArbNumerics.cos(eta)/ArbNumerics.sin(eta)
    thrad[2] = 2im*(1+ArbNumerics.sin(eta))/ArbNumerics.sin(eta)*z-1im*angularm*ArbNumerics.cos(eta)/ArbNumerics.sin(eta)
    thrad[3] = (4*(mu^2-2*z^2))/(alpha)
    zrad = 2*alpha*ArbNumerics.sin(eta)

    extrafac= -(4*(mu^2 - 2*z^2)^2)/(alpha^2)+(2*(mu^2-2*z^2))/alpha+1im*alpha*(2*z-angularm*ArbNumerics.cos(eta)+(1im+2*z)*ArbNumerics.sin(eta))+(1/2)*(3*mu^2-15*z^2-mu^2*ArbNumerics.cos(2*eta)+z^2*ArbNumerics.cos(2*eta)+4*mu^2*ArbNumerics.sin(eta)-8*z^2*ArbNumerics.sin(eta))
    
    function func1(x)
        return equation(thrad,x,q+extrafac,zrad)
    end
    sig0 = muller( func1, ArbComplex(0.5 - 0.5im), false )[1]
        
    # QNMs condition for ingoing and outgoing waves at the event horizon and infinity.

    ess0 = ArbNumerics.exp(-1im*pi*sig0)*ArbNumerics.sinpi((thrad[2]+thrad[1]+sig0)/2) *
           ArbNumerics.sinpi((thrad[2]-thrad[1]+sig0)/2)*ArbNumerics.sinpi((thrad[3]+sig0)/2) /
           ArbNumerics.sinpi((thrad[2]+thrad[1]-sig0)/2)/ArbNumerics.sinpi((thrad[2]-thrad[1]-sig0)/2)/
           ArbNumerics.sinpi((thrad[3]-sig0)/2) 
    
    return tauhat(thrad,sig0,ess0,zrad)
end

function fr_wrapper(x)
    res = zeros(ArbComplex,2)
    res[1] = angulareq(x[1],x[2])
    res[2] = radialeq(x[1],x[2])
    return res
end

# This part of the script computes the frequencies for different values of 'a/M' with 'M*mu' fixed.

# Comment: M*mu = mu

mu = 0.1		


function rang(ini)
    i = ini
    #arq = open("n0l1m1_mu_0.1_a.txt", "w")
    initial = zeros(ArbComplex,2)
    initial[1] = ArbComplex(0.29741566124545843 - 0.09495707360620889im)
    initial[2] = ArbComplex(2.0 + 4.235579710378251e-35im)
    while ( i < 0.11)
      global eta = ArbNumerics.acos(i)
      solution = newton2d(fr_wrapper, initial, true)
	
      println("a/M=",ArbFloat(i,digits=12),",","\t","omeg=",Complex{Float64}(solution[1][1]),",","\t","lamb=",Complex{Float64}(solution[1][2]))

      #println(arq,ArbFloat(i,digits=12),",","\t",Complex{Float64}(solution[1][1]),",","\t",Complex{Float64}(solution[1][2]))

	initial[1] = solution[1][1]
	initial[2] = solution[1][2]

        i += 0.01 #increment
    end
    #return close(arq)
end

#start point for the range
rang(0.0)

# This part of the script computes the frequencies for different values of 'M*mu' with 'a/M' fixed.

# Comment a/M = Cos(eta)

eta = ArbNumerics.acos(0.0)		


function rang(ini)
    i = ini
    #arq = open("n0l1m1_a_0.0_mu.txt", "w")
    initial = zeros(ArbComplex,2)
    initial[1] = ArbComplex(0.29293613326728274 - 0.09765998891357822im)
    initial[2] = ArbComplex(2.0 + 4.29053371197775e-35im)
    while ( i < 0.11)
      global mu = i
      solution = newton2d(fr_wrapper, initial, true)
	
      println("M*mu=",ArbFloat(i,digits=9),",","\t","omeg=",Complex{Float64}(solution[1][1]),",","\t","lamb=",Complex{Float64}(solution[1][2]))
      
      #println(arq,ArbFloat(i,digits=9),",","\t",Complex{Float64}(solution[1][1]),",","\t",Complex{Float64}(solution[1][2]))

	initial[1] = solution[1][1]
	initial[2] = solution[1][2]

        i += 0.01 #increment
    end
    #return close(arq)
end

#start point for the range
rang(0.0)

