BlackHawk Version 2.3 (14 May 2024)
-----------------------------------
By <PERSON> (<EMAIL>) and <PERSON> (<EMAIL>)

INTRODUCTION
------------
The most recent version of this program can be obtained from: 
https://blackhawk.hepforge.org/

This C code (in C99 standard) provides a detailed computation of the Hawking emission spectrum
for any mass and spin distribution of Black Holes in diverse metric (Kerr, Re<PERSON>ner-Nordström,
higher-dimensional, polymerized).

The program has been tested on Linux, Mac and Windows (using Cygwin) machines with gcc, clang and icc.

If you use BlackHawk to write a paper, please cite:

<PERSON><PERSON> and <PERSON><PERSON>, Eur. Phys. J. C79 (2019) 693, arXiv:1905.04268 [gr-qc]
<PERSON><PERSON> and <PERSON><PERSON>, Eur. Phys. J. C81 (2021) 910, arXiv:2108.02737 [gr-qc]

as well as PYTH<PERSON>, HERWIG, <PERSON><PERSON>ma or HDMSpectra (respectively) depending on the choice of hadronization code:

<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. <PERSON>, <PERSON>. <PERSON>, <PERSON>. <PERSON>ten, S. <PERSON><PERSON>na, S. <PERSON>stel, C. O. Rasmussen, and P. Z.
Skands, Comput. Phys. Commun. 191, 159 (2015), arXiv:1410.3012 [hep-ph]
J. <PERSON>m et al., Eur. Phys. J. C76, 196 (2016), arXiv:1512.01178 [hep-ph]
A. Coogan, L. Morrison, and S. Profumo, JCAP 01, 056, arXiv:1907.11846 [hep-ph]
C. W. Bauer, N. L. Rodd, B. R. Webber, JHEP 06 (2021) 121, arXiv:2007.15001 [hep-ph]

Installation and Compilation
----------------------------
- tar xzvf blackhawk_vX.X.tgz
- cd blackhawk_vX.X
- in Makefile, define your C compiler
- compile with: make
- create the executable with: make BlackHawk_*, where * is "tot" or "inst"

Included Files
--------------
- Procedures in src/:
evolution.c general.c primary.c secondary.c spectrum.c technical.c hadro_herwig.c hadro_pythia.c hadro_pythianew.c hadro_hazma.c hadro_hdmspectra.c

- Main programs:
BlackHawk_inst.c: calculation of the instantaneous Hawking spectra
BlackHawk_tot.c: calculation of the time-dependent Hawking spectra

- Headers in src/:
include.h: definitions and prototypes

- src/tables/:
Numerical tables used in the code

- manuals/:
Two .pdf for the up-to-date manuals of the code

- results/:
The folder where run data will be saved. The result are given in CGS units.

- scripts/:
C, C++ and Mathematica scripts used to compute the numerical tables (cosmology_scripts/, greybody_scripts/, herwig_scripts/, pythia_scripts/ and pythianew_scripts/)

- Others:
Makefile(s)
README.txt files


History
-------

v1.0 - 22/05/2019 - First public release.
v1.1 - 14/11/2019 - Various bugs corrected. Addition of a spin distribution.
V2.0 - 06/08/2021 - Non standard BH metrics. Dark matter emission. Corrected PYTHIA tables. Various bug corrections.
v2.1 - 20/11/2021 - Various bugs corrected.
v2.2 - 15/09/2023 - HDMSpectra spectra added.
v2.3 - 14/05/2024 - Various bugs corrected.

LICENSE
-------
    BlackHawk Copyright (C) 2019-2024 A. Arbey, J. Auffinger

    This program is a free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or any 
    later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
    GNU General Public License for more details.

    See <http://www.gnu.org/licenses/>.  
