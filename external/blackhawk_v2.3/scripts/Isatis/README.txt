				#####################
				#		    		#
				# BlackHawk scripts #
				#		    		#
				#####################



This program computes automatically the PBH constraints from a set of instruments.
For an example, see arXiv:XXXX.XXXXX.

author : <PERSON>�<PERSON>, j.au<PERSON><PERSON>@ipnl.in2p3.fr
last modified : 4 January 2021

#########################################################################################

This folder contains several elements:

- a folder BH_launcher/ that contains the program BH_launcher designed to launch BlackHawk
several times with varying parameters and to create the argument file fitted to <PERSON><PERSON> (see
below)

- a folder constraint/photons/ that contains all the tabulated instrument fluxes, effective
areas, and backgrounds available inside Isatis

- a file Isatis.c that contains all the Isatis routines

- a file Makefile that is used to compile Isatis

- a file parameters.txt that contains the parameters of a run of Isatis

- a Python script plotting.py that allows to plot rapidly the Isatis constraints

#########################################################################################

To compile Isatis.c into Isatis.x just type

make Isatis

from the terminal in this folder. Then, launch Isatis with two positional arguments:

- the parameters file "parameters.txt" that contains the parameters for the Isatis run

- a file that contains the number and name of a set of BlackHawk runs, of the same format
as those generated by the program BH_launcher

So that you just type in the terminal:

./Isatis.x parameters.txt [run_file]

#########################################################################################

Isatis generates a single output file, whose name is fixed by the user, and that contains
the constraint f_PBH on the PBH DM fraction for all instruments implemented inside Isatis
and all runs listed in the run_file.

#########################################################################################

The parameters that you can modify, inside parameters.txt are:

- output: this is the name of the output file generated by Isatis, in the form results_photons_*.txt

- path: this is the path of your version of BlackHawk; if Isatis is at its default location
please do not modify this parameter

- sessions: BlackHawk programs that you have launched for your runs (0: instantaneous only,
1: time-dependent only, 2: both)

- local_DM_GeV: the DM local density in GeV/cm^3

- global_DM: the DM cosmological density today in g/cm^3

- r_0: the distance from the Sun to the GC in kpc

- profile: the density profile of the galaxy (0: generalized NFW, 1: Einasto)

- rho_c_halo: the characteristic halo density in g/cm^3

- r_c_halo: the characteristic halo radius in kpc

- gamma_halo: the halo inner slope

- t_eq: the matter-radiation equality time in s

- t_CMB: the CMB last scattering surface time in s

- t_today: the age of the universe in s

- domination: the redshift history (0: standard redshift history, 1: full radiation domination,
2: full matter domination)

- energy_method: the way signal is compared to data (0: full energy range, 1: binned energy
range)

- background_type: the background choice (0: gal+egal arXiv:2010.04797, 1: gal+egal
arXiv:2110.03333, 2: gal arXiv:1101.1381 and egal arXiv:1906.04750)

- confidence_level: the number of error bars admitted in the comparison (data +
confidence_level*error_bar); negative values of CL correspond to lower error bar (be careful
that this can generate negative results)

- signal_to_noise_ratio: the signal to noise ratio for prospective instruments

#########################################################################################

The user an add other instruments in the Isatis list by providing their measured flux (existing
instruments, with format E, DeltaE-, DeltaE+, flux, Deltaflux-, Deltaflux+) or their effective
area (prospective instruments). Other backgrounds can also be considered by providing the
tabulated values. These files must be placed inside the Isatis/constraints/photons/ folder.
The user must also add a line for the new instrument inside the main routine, and a list of
technical characteristics inside the relevant routines fill_type1(), fill_type2() and
energy_resolution(). Different targets (other than the GC), statistical methods, halo profiles etc.
would require that the user implements the corresponding new routines.

#########################################################################################

If you have any difficulty using Isatis, please contact the author.