				#####################
				#		    		#
				# BlackHawk scripts #
				#		    		#
				#####################


authors : <PERSON>, alexand<PERSON>.<EMAIL> & <PERSON>�<PERSON>, jere<PERSON>.<EMAIL>
	  
last modified : 07 April 2024

#########################################################################################

This folder contains several sub-folders:

- cosmology scripts: a script that computes the stacked spectra through redshift evolution

- greybody_scripts (itself divided into fM_scripts and greybody_factors): scripts to
compute the greybody factors and the f(M,*) and g(M,*) factors

- herwig_scripts: scripts to compute the HERWIG hadronization tables (BBN epoch)

- pythia_scripts: scripts to compute the PYTHIA hadronization tables (BBN epoch)

- pythia_new_scripts: scripts to compute the PYTHIA hadronization tables (present epoch)

- hazma_scripts: scripts to compute the Hazma hadronization tables (present epoch, low energy)

- superiso_scripts: scripts to compute AMS and FERMI-LAT constraints on PBHs

- visualization_scripts: scripts to plot the BlackHawk data

#########################################################################################

Each one of the sub-folders contains it own README file giving instructions on how to use
the scripts, and the programs needed to do so. These scripts can be used, modified and
adapted to the user's need without asking for the authors. If you have any issue with
them, please contact us.






















