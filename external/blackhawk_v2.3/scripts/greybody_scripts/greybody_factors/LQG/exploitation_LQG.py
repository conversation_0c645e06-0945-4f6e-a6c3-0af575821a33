##
# This script computes the high and low fitting parameters for polymerized BHs. The "blah" mentions should be replaced with the correct path to your tables/figures.

import numpy as np
import scipy.interpolate as scp
import scipy.integrate as sci
import pylab
from scipy.special import gamma
from scipy import stats

## Exploiting LQG BHs results
# greybody factors for polymerized BHs
data_0 = np.genfromtxt("blah")
data_1 = np.genfromtxt("blah")
data_2 = np.genfromtxt("blah")
data_12 = np.genfromtxt("blah")

# comparative greybody factors for Schwarzschild BHs
data_0_comp = np.genfromtxt("blah")
data_1_comp = np.genfromtxt("blah")
data_2_comp = np.genfromtxt("blah")
data_12_comp = np.genfromtxt("blah")


# data at low energy, you can produce those by modifying the energy bounds in the Mathematica scripts
data_0_low = np.genfromtxt("blah")
data_1_low = np.genfromtxt("blah")
data_2_low = np.genfromtxt("blah")
data_12_low = np.genfromtxt("blah")

data_0_comp_low = np.genfromtxt("blah")
data_1_comp_low = np.genfromtxt("blah")
data_2_comp_low = np.genfromtxt("blah")
data_12_comp_low = np.genfromtxt("blah")

# be careful to reproduce the energy range you have used for the computations
enumber = len(data_0[0])
energies = np.zeros(enumber)
Emin = 0.01
Einter = 1.
Emax = 5.
for i in range(len(energies)):
    if i<100:
        energies[i] = 10**(np.log10(Emin) + (np.log10(Einter) - np.log10(Emin))/(100.)*i)
    else:
        energies[i] = Einter + (Emax - Einter)/99.*(i-100.)

energies_low = np.zeros(50)
for i in range(50):
    energies_low[i] = 10**(-6 + (-4 - (-6))/(50 - 1)*i)

index_min = 25
index_max = 150

epsilon = np.array([10**(-1.),10**(-0.9),10**(-0.8),10**(-0.7),10**(-0.6),10**(-0.5),10**(-0.4),10**(-0.3),10**(-0.2),10**(-0.1)])

M = 0.5

# put here the correct value of a_0 corresponding to your computations
a0 = 0.

def P(epsilon):
    return (np.sqrt(1+epsilon**2)-1)/(np.sqrt(1+epsilon**2)+1)

def m(M,epsilon):
    return M/(1+P(epsilon))**2

def rplus(M,epsilon):
    return 2*m(M,epsilon)

def rminus(M,epsilon):
    return rplus(M,epsilon)*P(epsilon)**2

def T(M,epsilon):
    return 4*m(M,epsilon)**3*(1-P(epsilon)**2)/(32*np.pi*m(M,epsilon)**4+2*np.pi*a0**2)

## spin 0
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_0)$")
ax.scatter(np.log10(energies_low),np.log10(data_0_comp_low*(np.exp(energies_low/T(M,0))-1.)),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[0,:]*(np.exp(energies_low/T(M,epsilon[0]))-1.)),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[4,:]*(np.exp(energies_low/T(M,epsilon[4]))-1.)),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[9,:]*(np.exp(energies_low/T(M,epsilon[9]))-1.)),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_0_comp_low*(np.exp(energies_low/T(M,0))-1.)))

slopes = np.zeros(10)
intercepts = np.zeros(10)

for i in range(0,10):
    result = stats.linregress(np.log10(energies_low),np.log10(data_0_low[i,:]*(np.exp(energies_low/T(M,epsilon[i]))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 2,color = "black",linestyle = "--")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[9]*np.log10(energies_low)+intercepts[9],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_0$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_0_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_0[0,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[0]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_0[4,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[4]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_0[9,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[9]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$\\varepsilon = 10^{-1}$")


limit = 0.
for i in range(index_max,len(data_0[0])):
    limit += data_0_comp[i]*(np.exp(energies[i]/T(M,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_0[0])-index_max)

limits = np.zeros(10)
for j in range(10):
    for i in range(index_max,len(data_0[0])):
        limits[j] += data_0[j,i]*(np.exp(energies[i]/T(M,epsilon[j]))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_0[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[9],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

spin_0_path = "blah"

print("%15s%15s%15s%15s\n"%(spin_0_path,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_0_path,"a"),end = "")
for i in range(10):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(epsilon[i],slopes[i],intercepts[i],limits[i]),file=open(spin_0_path,"a"),end = "")

## spin 1
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_1)$")
ax.scatter(np.log10(energies_low),np.log10(data_1_comp_low*(np.exp(energies_low/T(M,0))-1.)),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[0,:]*(np.exp(energies_low/T(M,epsilon[0]))-1.)),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[4,:]*(np.exp(energies_low/T(M,epsilon[4]))-1.)),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[9,:]*(np.exp(energies_low/T(M,epsilon[9]))-1.)),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_1_comp_low*(np.exp(energies_low/T(M,0))-1.)))

slopes = np.zeros(10)
intercepts = np.zeros(10)

for i in range(0,10):
    result = stats.linregress(np.log10(energies_low),np.log10(data_1_low[i,:]*(np.exp(energies_low/T(M,epsilon[i]))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 2,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[9]*np.log10(energies_low)+intercepts[9],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_1$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_1_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_1[0,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[0]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_1[4,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[4]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(energies[index_max:],data_1[9,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[9]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")

limit = 0.
for i in range(index_max,len(data_1[0])):
    limit += data_1_comp[i]*(np.exp(energies[i]/T(M,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_1[0])-index_max)

limits = np.zeros(10)
for j in range(10):
    for i in range(index_max,len(data_1[0])):
        limits[j] += data_1[j,i]*(np.exp(energies[i]/T(M,epsilon[j]))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_1[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[9],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

spin_1_path = "blah"

print("%15s%15s%15s%15s\n"%("epsilon/fits","a1","b1","limit"),file=open(spin_1_path,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_1_path,"a"),end = "")
for i in range(50):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(epsilon[i],slopes[i],intercepts[i],limits[i]),file=open(spin_1_path,"a"),end = "")

## spin 2
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_2)$")
ax.scatter(np.log10(energies_low),np.log10(data_2_comp_low*(np.exp(energies_low/T(M,0))-1.)),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[0,:]*(np.exp(energies_low/T(M,epsilon[0]))-1.)),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[4,:]*(np.exp(energies_low/T(M,epsilon[4]))-1.)),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[9,:]*(np.exp(energies_low/T(M,epsilon[9]))-1.)),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_2_comp_low*(np.exp(energies_low/T(M,0))-1.)))

slopes = np.zeros(10)
intercepts = np.zeros(10)

for i in range(0,10):
    result = stats.linregress(np.log10(energies_low),np.log10(data_2_low[i,:]*(np.exp(energies_low/T(M,epsilon[i]))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 2,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[9]*np.log10(energies_low)+intercepts[9],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_2$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_2_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_2[0,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[0]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_2[4,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[4]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(energies[index_max:],data_2[9,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[9]))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")

limit = 0.
for i in range(index_max,len(data_2[0])):
    limit += data_2_comp[i]*(np.exp(energies[i]/T(M,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_2[0])-index_max)

limits = np.zeros(10)
for j in range(10):
    for i in range(index_max,len(data_2[0])):
        limits[j] += data_2[j,i]*(np.exp(energies[i]/T(M,epsilon[j]))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_2[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[9],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

spin_2_path = "blah"

print("%15s%15s%15s%15s\n"%("epsilon/fits","a1","b1","limit"),file=open(spin_2_path,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_2_path,"a"),end = "")
for i in range(10):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(epsilon[i],slopes[i],intercepts[i],limits[i]),file=open(spin_2_path,"a"),end = "")

## spin 1/2
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_{1/2})$")
ax.scatter(np.log10(energies_low),np.log10(data_12_comp_low*(np.exp(energies_low/T(M,0))+1.)),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[0,:]*(np.exp(energies_low/T(M,epsilon[0]))+1.)),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[4,:]*(np.exp(energies_low/T(M,epsilon[4]))+1.)),s = 1,color = "green",label = "$\\varepsilon = 10^{-0.6}$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[9,:]*(np.exp(energies_low/T(M,epsilon[9]))+1.)),s = 1,color = "red",label = "$\\varepsilon = 10^{-0.1}$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_12_comp_low*(np.exp(energies_low/T(M,0))+1.)))

slopes = np.zeros(10)
intercepts = np.zeros(10)

for i in range(0,10):
    result = stats.linregress(np.log10(energies_low),np.log10(data_12_low[i,:]*(np.exp(energies_low/T(M,epsilon[i]))+1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 0.5,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[9]*np.log10(energies_low)+intercepts[9],linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_{1/2}$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_12_comp[index_max:]*(np.exp(energies[index_max:]/T(M,0))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "${\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_12[0,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[0]))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_12[4,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[4]))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$\\varepsilon = 10^{-1}$")
ax.scatter(energies[index_max:],data_12[9,index_max:]*(np.exp(energies[index_max:]/T(M,epsilon[9]))+1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$\\varepsilon = 10^{-1}$")

limit = 0.
for i in range(index_max,len(data_12[0])):
    limit += data_12_comp[i]*(np.exp(energies[i]/T(M,0))+1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_12[0])-index_max)

limits = np.zeros(10)
for j in range(10):
    for i in range(index_max,len(data_12[0])):
        limits[j] += data_12[j,i]*(np.exp(energies[i]/T(M,epsilon[j]))+1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_12[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[9],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")

ax.legend(loc = "best")
f.show()

spin_12_path = "blah"

print("%15s%15s%15s%15s\n"%("epsilon/fits","a1","b1","limit"),file=open(spin_12_path,"w"),end = "")
print("%15.5e%15.5e%15.5e%15.5e\n"%(0.,slope,intercept,limit),file=open(spin_12_path,"a"),end = "")
for i in range(10):
    print("%15.5e%15.5e%15.5e%15.5e\n"%(epsilon[i],slopes[i],intercepts[i],limits[i]),file=open(spin_12_path,"a"),end = "")

## Comparison to theory for all spins, high energy
fig_width_pt = 85*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 10,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 10,
          'legend.fontsize': 10,
          'xtick.labelsize': 10,
          'ytick.labelsize': 10,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(2)
f.clf()
ax = f.add_subplot(111)

fits0 = np.genfromtxt("C:/Users/<USER>/Documents/thèse/greybody factors/other_metrics/LQG/spin_0_fits.txt",skip_header = 1)
fits1 = np.genfromtxt("C:/Users/<USER>/Documents/thèse/greybody factors/other_metrics/LQG/spin_1_fits.txt",skip_header = 1)
fits2 = np.genfromtxt("C:/Users/<USER>/Documents/thèse/greybody factors/other_metrics/LQG/spin_2_fits.txt",skip_header = 1)
fits12 = np.genfromtxt("C:/Users/<USER>/Documents/thèse/greybody factors/other_metrics/LQG/spin_12_fits.txt",skip_header = 1)

# put here the path to the numerically computed ratios ("High energy limits.txt")
num = np.genfromtxt("blah")

ax.set_xscale("linear")
ax.set_xlabel("$\\varepsilon$")
ax.set_ylabel("$\\beta_{\infty}^{\\rm LQG}$")
ax.set_xlim(0,1)
ax.scatter(data0[:,0],data0[:,3],s = 10,marker = "+",color = "black",label = "${\\rm spin\,\,0}$")
ax.scatter(data1[:,0],data1[:,3],s = 10,marker = "x",color = "black",label = "${\\rm spin\,\,1}$")
ax.scatter(data2[:,0],data2[:,3],s = 10,marker = "o",color = "black",label = "${\\rm spin\,\,2}$")
ax.scatter(data12[:,0],data12[:,3],s = 10,marker = "s",color = "black",label = "${\\rm spin\,\,1/2}$")
ax.plot(num[::2],num[1::2],color = "red",linewidth = 1,label = "${\\rm theory}$")
ax.legend(loc = "best")

f.tight_layout(rect = [0,0,1,1])
f.show()
f.savefig("blah")

## Comparison between all spins for low energy
fig_width_pt = 85*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 10,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 10,
          'legend.fontsize': 10,
          'xtick.labelsize': 10,
          'ytick.labelsize': 10,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

nbre = 100
ee = np.zeros(nbre)
emin = 0
emax = 0.9999
for i in range(nbre):
    ee[i] = (1.-10**(np.log10(1-emin) + (np.log10(1-emax)-np.log10(1-emin))/(nbre-1)*i))
beta_0 = np.zeros(len(ee))
for i in range(len(ee)):
    beta_0[i] = 16.*m(M,ee[i])**2*(1+a0**2/(16.*m(M,ee[i])**4))*(1+P(ee[i])**2)/(4.*rplus(M,0)**2)

f = pylab.figure(5)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("log")
ax.set_xlim(0,1)
ax.set_ylim(1e-2,2e+0)
ax.set_xlabel("$\\varepsilon$")
ax.set_ylabel("$\\beta_s^{\\rm LQG}$")
ax.scatter(fits_0[:,0],10**fits_0[:,2]/4.,marker = "+",s = 10,color = "blue",label = "${\\rm spin\,\,0}$")
ax.plot(ee,beta_0,linewidth = 1,color = "blue")
ax.scatter(fits_1[:,0],10**fits_1[:,2]/4.*3.,marker = "x",s = 10,color = "green",label = "${\\rm spin\,\,1}$")
ax.plot(ee,beta_0*100.,linewidth = 1,color = "green")
ax.scatter(fits_2[:,0],10**fits_2[:,2]/4.*45.,marker = "o",s = 10,color = "red",label = "${\\rm spin\,\,2}$")
ax.plot(ee,beta_0*100.,linewidth = 1,color = "red")
ax.scatter(fits_12[:,0],10**fits_12[:,2]*2.,marker = "s",s = 10,color = "purple",label = "${\\rm spin\,\,1/2}$")
ax.plot(ee,beta_0*100.,linewidth = 1,color = "purple")
ax.legend(loc = "best",ncol = 1)
f.tight_layout(rect = [0,0,1,1])
f.show()
f.savefig("blah")

## Creating the formatted tables
enumber = len(data_0[0])
energies = np.zeros(enumber)
Emin = 0.01
Einter = 1.
Emax = 5.
for i in range(len(energies)):
    if i<100:
        energies[i] = 10**(np.log10(Emin) + (np.log10(Einter) - np.log10(Emin))/(100.)*i)
    else:
        energies[i] = Einter + (Emax - Einter)/99.*(i-100.)

epsilon = np.array([10**(-1.),10**(-0.9),10**(-0.8),10**(-0.7),10**(-0.6),10**(-0.5),10**(-0.4),10**(-0.3),10**(-0.2),10**(-0.1)])

# Here enter the paths for the formatted greybody factors tables
spin_0_format = ""
spin_1_format = ""
spin_2_format = ""
spin_12_format = ""

print("%15s"%("epsilon/x"),file=open(spin_0_format,"w"),end = "")
for i in range(len(energies)):
    print("%15.5e"%(energies[i]),file=open(spin_0_format,"a"),end = "")
print("\n",file=open(spin_0_format,"a"),end = "")
print("%15.5f"%(0.),file=open(spin_0_format,"a"),end = "")
for i in range(len(data_0_comp)):
    print("%15.5e"%(data_0_comp[i]),file=open(spin_0_format,"a"),end = "")
print("\n",file=open(spin_0_format,"a"),end = "")
for i in range(len(epsilon)):
    print("%15.5f"%(epsilon[i]),file=open(spin_0_format,"a"),end = "")
    for j in range(len(data_0[0])):
        print("%15.5e"%(data_0[i,j]),file=open(spin_0_format,"a"),end = "")
    print("\n",file=open(spin_0_format,"a"),end = "")

print("%15s"%("epsilon/x"),file=open(spin_1_format,"w"),end = "")
for i in range(len(energies)):
    print("%15.5e"%(energies[i]),file=open(spin_1_format,"a"),end = "")
print("\n",file=open(spin_1_format,"a"),end = "")
print("%15.5f"%(0.),file=open(spin_1_format,"a"),end = "")
for i in range(len(data_1_comp)):
    print("%15.5e"%(data_1_comp[i]),file=open(spin_1_format,"a"),end = "")
print("\n",file=open(spin_1_format,"a"),end = "")
for i in range(len(epsilon)):
    print("%15.5f"%(epsilon[i]),file=open(spin_1_format,"a"),end = "")
    for j in range(len(data_1[0])):
        print("%15.5e"%(data_1[i,j]),file=open(spin_1_format,"a"),end = "")
    print("\n",file=open(spin_1_format,"a"),end = "")

print("%15s"%("epsilon/x"),file=open(spin_2_format,"w"),end = "")
for i in range(len(energies)):
    print("%15.5e"%(energies[i]),file=open(spin_2_format,"a"),end = "")
print("\n",file=open(spin_2_format,"a"),end = "")
print("%15.5f"%(0.),file=open(spin_2_format,"a"),end = "")
for i in range(len(data_2_comp)):
    print("%15.5e"%(data_2_comp[i]),file=open(spin_2_format,"a"),end = "")
print("\n",file=open(spin_2_format,"a"),end = "")
for i in range(len(epsilon)):
    print("%15.5f"%(epsilon[i]),file=open(spin_2_format,"a"),end = "")
    for j in range(len(data_2[0])):
        print("%15.5e"%(data_2[i,j]),file=open(spin_2_format,"a"),end = "")
    print("\n",file=open(spin_2_format,"a"),end = "")

print("%15s"%("epsilon/x"),file=open(spin_12_format,"w"),end = "")
for i in range(len(energies)):
    print("%15.5e"%(energies[i]),file=open(spin_12_format,"a"),end = "")
print("\n",file=open(spin_12_format,"a"),end = "")
print("%15.5f"%(0.),file=open(spin_12_format,"a"),end = "")
for i in range(len(data_12_comp)):
    print("%15.5e"%(data_12_comp[i]),file=open(spin_12_format,"a"),end = "")
print("\n",file=open(spin_12_format,"a"),end = "")
for i in range(len(epsilon)):
    print("%15.5f"%(epsilon[i]),file=open(spin_12_format,"a"),end = "")
    for j in range(len(data_12[0])):
        print("%15.5e"%(data_12[i,j]),file=open(spin_12_format,"a"),end = "")
    print("\n",file=open(spin_12_format,"a"),end = "")

