(* Content-type: application/vnd.wolfram.mathematica *)

(*** Wolfram Notebook File ***)
(* http://www.wolfram.com/nb *)

(* CreatedBy='Mathematica 11.1' *)

(*CacheID: 234*)
(* Internal cache information:
NotebookFileLineBreakTest
NotebookFileLineBreakTest
NotebookDataPosition[       158,          7]
NotebookDataLength[     39674,       1000]
NotebookOptionsPosition[     38309,        965]
NotebookOutlinePosition[     38682,        981]
CellTagsIndexPosition[     38639,        978]
WindowFrame->Normal*)

(* Beginning of Notebook Content *)
Notebook[{
Cell[BoxData[
 RowBox[{
  RowBox[{"ClearAll", "[", "\"\<Global`*\>\"", "]"}], ";"}]], "Input",
 CellChangeTimes->{{3.761026189169488*^9, 
  3.761026200575509*^9}},ExpressionUUID->"49eafb7e-4cf2-44f9-89e0-\
8d2b18b527fc"],

Cell[BoxData[{
 RowBox[{
  RowBox[{"SetDirectory", "[", 
   RowBox[{"NotebookDirectory", "[", "]"}], "]"}], ";"}], "\n", 
 RowBox[{
  RowBox[{"nbener", " ", "=", " ", "200"}], ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"nba", "=", "50"}], ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"aet", " ", "=", " ", 
   RowBox[{"Table", "[", 
    RowBox[{"0", ",", 
     RowBox[{"{", 
      RowBox[{"i", ",", "1", ",", "nba"}], "}"}]}], "]"}]}], ";"}], "\n", 
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"i", "=", "30"}], ",", 
    RowBox[{"i", "<", 
     RowBox[{"nba", "+", "1"}]}], ",", 
    RowBox[{"i", "++"}], ",", 
    RowBox[{
     RowBox[{"aet", "[", 
      RowBox[{"[", 
       RowBox[{"50", "+", "30", "-", "i"}], "]"}], "]"}], " ", "=", " ", 
     RowBox[{"1", "-", 
      RowBox[{"10", "^", 
       RowBox[{"(", 
        RowBox[{
         RowBox[{"Log10", "[", "0.0001", "]"}], "+", 
         RowBox[{
          RowBox[{
           RowBox[{"(", 
            RowBox[{
             RowBox[{"Log10", "[", "0.1", "]"}], "-", 
             RowBox[{"Log10", "[", "0.0001", "]"}]}], ")"}], "/", 
           RowBox[{"(", 
            RowBox[{"nba", "-", "30"}], ")"}]}], "*", 
          RowBox[{"(", 
           RowBox[{"i", "-", "30"}], ")"}]}]}], ")"}]}]}]}]}], "]"}], 
  ";"}], "\n", 
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"i", "=", "2"}], ",", 
    RowBox[{"i", "<", "10"}], ",", 
    RowBox[{"i", "++"}], ",", 
    RowBox[{
     RowBox[{"aet", "[", 
      RowBox[{"[", "i", "]"}], "]"}], " ", "=", " ", 
     RowBox[{"10", "^", 
      RowBox[{"(", 
       RowBox[{
        RowBox[{"Log10", "[", "0.001", "]"}], "+", 
        RowBox[{
         RowBox[{
          RowBox[{"(", 
           RowBox[{
            RowBox[{"Log10", "[", "0.1", "]"}], "-", 
            RowBox[{"Log10", "[", "0.001", "]"}]}], ")"}], "/", "7"}], "*", 
         RowBox[{"(", 
          RowBox[{"i", "-", "2"}], ")"}]}]}], ")"}]}]}]}], "]"}], ";"}], "\n", 
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"i", "=", "9"}], ",", 
    RowBox[{"i", "<", "31"}], ",", 
    RowBox[{"i", "++"}], ",", 
    RowBox[{
     RowBox[{"aet", "[", 
      RowBox[{"[", "i", "]"}], "]"}], "=", 
     RowBox[{"0.1", "+", 
      RowBox[{
       RowBox[{
        RowBox[{"(", 
         RowBox[{"0.9", "-", "0.1"}], ")"}], "/", "21"}], "*", 
       RowBox[{"(", 
        RowBox[{"i", "-", "9"}], ")"}]}]}]}]}], "]"}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"omega", " ", "=", " ", 
   RowBox[{"Table", "[", 
    RowBox[{
     RowBox[{"10", "^", 
      RowBox[{"(", 
       RowBox[{
        RowBox[{"Log10", "[", "0.01", "]"}], "+", 
        RowBox[{
         RowBox[{
          RowBox[{"(", 
           RowBox[{
            RowBox[{"Log10", "[", "5.", "]"}], "-", 
            RowBox[{"Log10", "[", "0.01", "]"}]}], ")"}], "/", 
          RowBox[{"(", 
           RowBox[{"nbener", "-", "1"}], ")"}]}], "*", "i"}]}], ")"}]}], ",", 
     RowBox[{"{", 
      RowBox[{"i", ",", "0", ",", 
       RowBox[{"nbener", "-", "1"}]}], "}"}]}], "]"}]}], ";"}], "\n", 
 RowBox[{
  RowBox[{"data", " ", "=", " ", 
   RowBox[{"ReadList", "[", 
    RowBox[{"\"\<test_0.5_gM.txt\>\"", ",", "Number"}], "]"}]}], ";"}], "\n", 
 RowBox[{
  RowBox[{"data", " ", "=", " ", 
   RowBox[{"ArrayReshape", "[", 
    RowBox[{"data", ",", 
     RowBox[{"{", 
      RowBox[{"nba", ",", "nbener"}], "}"}]}], "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"fits", "=", " ", 
   RowBox[{"Table", "[", 
    RowBox[{
     RowBox[{"Table", "[", 
      RowBox[{"0", ",", 
       RowBox[{"{", 
        RowBox[{"i", ",", "1", ",", "7"}], "}"}]}], "]"}], ",", 
     RowBox[{"{", 
      RowBox[{"j", ",", "1", ",", "nba"}], "}"}]}], "]"}]}], ";"}]}], "Input",
 CellChangeTimes->{{3.761026189169488*^9, 3.7610262796134553`*^9}, {
   3.7610301157158318`*^9, 3.7610301162618327`*^9}, {3.7610310923897653`*^9, 
   3.7610311132020035`*^9}, {3.761547846272032*^9, 3.761547847007233*^9}, {
   3.7615503113134766`*^9, 3.7615503116984987`*^9}, {3.761550373171015*^9, 
   3.7615503749411163`*^9}, {3.761551225529767*^9, 3.761551225973792*^9}, {
   3.761558184240508*^9, 3.761558246046623*^9}, 3.761558289876704*^9, {
   3.7615588931744385`*^9, 3.761558893486439*^9}, {3.7615591188860626`*^9, 
   3.7615591198230643`*^9}, {3.761559636779537*^9, 3.761559637795539*^9}, {
   3.761559827141301*^9, 3.7615598274845014`*^9}, {3.761891567407881*^9, 
   3.761891569003084*^9}, {3.76189164352623*^9, 3.7618916476682377`*^9}, {
   3.7620628247813187`*^9, 3.762062826165121*^9}, {3.7620629612993913`*^9, 
   3.7620629615489917`*^9}, {3.762063168779066*^9, 3.762063170252469*^9}, {
   3.762063245835628*^9, 3.7620632460872283`*^9}, {3.7620633479398336`*^9, 
   3.762063349284436*^9}, {3.7620634298694057`*^9, 3.7620634302302065`*^9}, {
   3.7620635081273637`*^9, 3.7620635104431677`*^9}, {3.7620635770533066`*^9, 
   3.762063577365307*^9}, {3.7620662395625663`*^9, 3.7620662954574814`*^9}, {
   3.762067080988125*^9, 3.7620670813489256`*^9}, {3.7620671844911337`*^9, 
   3.7620671848031344`*^9}, {3.762067346716461*^9, 3.762067347670063*^9}, {
   3.762067500467827*^9, 3.7620675006082277`*^9}, {3.7620675888916063`*^9, 
   3.7620675893138065`*^9}, {3.762067686426206*^9, 3.7620676867538066`*^9}, {
   3.762067779443997*^9, 3.762067781709001*^9}, {3.7621663860307083`*^9, 
   3.7621663900189366`*^9}, {3.7621666094514875`*^9, 3.7621666096755*^9}, {
   3.7621666841927624`*^9, 3.762166684364772*^9}, {3.7621667656054187`*^9, 
   3.7621667657584276`*^9}, {3.7621672325151243`*^9, 3.762167232762139*^9}, {
   3.762167463684347*^9, 3.76216746513843*^9}, {3.7621675749097085`*^9, 
   3.7621675751577225`*^9}, {3.762173260589545*^9, 3.7621732619955473`*^9}, {
   3.762173385477976*^9, 3.7621733857743764`*^9}, {3.763209982848095*^9, 
   3.76321000182113*^9}, {3.7632102581398544`*^9, 3.7632102582958546`*^9}, {
   3.763210397015109*^9, 3.7632103973583097`*^9}, {3.763210513673321*^9, 
   3.7632105140019217`*^9}, {3.763210694066249*^9, 3.763210698846857*^9}, {
   3.7632107522101545`*^9, 3.763210752522155*^9}, {3.763210864784159*^9, 
   3.7632108661423616`*^9}, {3.763210906497835*^9, 3.7632109068254356`*^9}, {
   3.7632110524567003`*^9, 3.7632110537213025`*^9}, {3.763211086832363*^9, 
   3.7632110871599636`*^9}, {3.7632111674789095`*^9, 
   3.7632111984925656`*^9}, {3.763797454894808*^9, 3.763797462675622*^9}, {
   3.763797513932915*^9, 3.7637975141357155`*^9}, {3.7637977695525804`*^9, 
   3.7637977706611824`*^9}, {3.7637978263030834`*^9, 3.763797826583884*^9}, {
   3.763798081595148*^9, 3.7637980831083508`*^9}, {3.763798127018031*^9, 
   3.763798127268632*^9}, {3.763798395029519*^9, 3.7637984271849775`*^9}, {
   3.7638901258626413`*^9, 3.7638901320628524`*^9}, {3.7638901875245533`*^9, 
   3.763890187899954*^9}, {3.76389034435684*^9, 3.7638903466852446`*^9}, {
   3.7638903925907283`*^9, 3.7638903928403287`*^9}, {3.76389057862687*^9, 
   3.763890581191275*^9}, {3.76389061139573*^9, 3.76389061162973*^9}, {
   3.7638910683995676`*^9, 3.7638910707093716`*^9}, {3.7638911046804333`*^9, 
   3.763891105040234*^9}, {3.7650970947320223`*^9, 3.7650970953248234`*^9}, {
   3.765097330314252*^9, 3.7650973351084604`*^9}, {3.7650975767617006`*^9, 
   3.7650976033595486`*^9}, {3.765098321920256*^9, 3.765098325669263*^9}, {
   3.765103276777531*^9, 3.765103294986164*^9}, {3.7657963640541124`*^9, 
   3.7657963656765885`*^9}, {3.7657965972223816`*^9, 
   3.7657965981384296`*^9}, {3.7657972917637863`*^9, 3.765797293265859*^9}, {
   3.765797663705162*^9, 3.7657976841947775`*^9}, {3.765798190488467*^9, 
   3.7657981907124796`*^9}, {3.7657983931188483`*^9, 
   3.7657983952999516`*^9}, {3.7657985405183315`*^9, 
   3.7657985406113367`*^9}, {3.765798616320079*^9, 3.7657986166997*^9}, {
   3.765799433807175*^9, 3.765799458443941*^9}, {3.7657995001416054`*^9, 
   3.7657995003580103`*^9}},ExpressionUUID->"0eac3288-2c71-455c-b35c-\
45c567e7796d"],

Cell[BoxData[
 RowBox[{
  RowBox[{"(*", 
   RowBox[{
    RowBox[{"index", "=", "7"}], ";"}], "*)"}], "\[IndentingNewLine]", 
  RowBox[{
   RowBox[{
    RowBox[{"lima", "=", "2"}], ";"}], "\[IndentingNewLine]", 
   RowBox[{
    RowBox[{"lim", "=", "170"}], ";"}], "\[IndentingNewLine]", 
   RowBox[{"(*", 
    RowBox[{"ListPlot", "[", 
     RowBox[{
      RowBox[{"Table", "[", 
       RowBox[{
        RowBox[{"{", 
         RowBox[{
          RowBox[{"omega", "[", 
           RowBox[{"[", "i", "]"}], "]"}], ",", 
          RowBox[{"Log10", "[", 
           RowBox[{"data", "[", 
            RowBox[{"[", 
             RowBox[{"index", ",", "i"}], "]"}], "]"}], "]"}]}], "}"}], ",", 
        RowBox[{"{", 
         RowBox[{"i", ",", "lim", ",", "nbener"}], "}"}]}], "]"}], ",", 
      RowBox[{"PlotRange", "\[Rule]", "All"}]}], "]"}], "*)"}]}]}]], "Input",
 CellChangeTimes->{{3.761026189169488*^9, 3.761026305992304*^9}, {
   3.7615471372484236`*^9, 3.761547180611107*^9}, {3.761547460702664*^9, 
   3.7615474611560645`*^9}, {3.7615476609302654`*^9, 3.761547761611062*^9}, {
   3.7615478531782446`*^9, 3.761547908479751*^9}, {3.761547991963912*^9, 
   3.761547992432913*^9}, {3.761548602600769*^9, 3.76154860300837*^9}, {
   3.7615486687184916`*^9, 3.7615487169357843`*^9}, {3.761548751898049*^9, 
   3.7615488432948203`*^9}, {3.7615503190399184`*^9, 3.76155043918079*^9}, {
   3.761551228314926*^9, 3.761551319018114*^9}, {3.761558200848339*^9, 
   3.761558201755141*^9}, {3.761558294081112*^9, 3.761558313829561*^9}, {
   3.7618916226367846`*^9, 3.7618916230111847`*^9}, {3.761891657058855*^9, 
   3.7618917057975473`*^9}, 3.7618917685708637`*^9, {3.762062848680767*^9, 
   3.762062914686298*^9}, {3.7620629651343985`*^9, 3.762063036463543*^9}, {
   3.762063173480076*^9, 3.7620632793082952`*^9}, {3.7620633156059685`*^9, 
   3.7620636227652016`*^9}, {3.762066249694786*^9, 3.7620662800286493`*^9}, {
   3.762166466713323*^9, 3.762166467333359*^9}, {3.7621666169579167`*^9, 
   3.762166617971975*^9}, {3.7650971385319033`*^9, 3.7650971391247044`*^9}, {
   3.7650973391060677`*^9, 3.7650973584399033`*^9}, 3.7651033014137764`*^9, {
   3.7657964283414903`*^9, 3.7657964529213095`*^9}, {3.765796732575219*^9, 
   3.765796737493461*^9}, {3.7657972971720567`*^9, 3.7657972974894667`*^9}, {
   3.765797622571345*^9, 3.7657976310289664`*^9}, {3.7657976919647603`*^9, 
   3.765797701423176*^9}, {3.765797830077466*^9, 3.7657978399509525`*^9}, {
   3.765797949043543*^9, 3.7657980042466693`*^9}, {3.7657981951871023`*^9, 
   3.765798195495512*^9}, {3.7657982904726014`*^9, 3.765798293987979*^9}, {
   3.765798398540512*^9, 3.7657984019668846`*^9}, {3.76579854673604*^9, 
   3.765798547127063*^9}, {3.7657994625235405`*^9, 3.7657994627975564`*^9}, {
   3.7657995039953947`*^9, 
   3.7657995043040075`*^9}},ExpressionUUID->"5a69311c-b9be-493b-ac63-\
954c41c134e7"],

Cell[BoxData[{
 RowBox[{
  RowBox[{"f", "=", 
   RowBox[{"Table", "[", 
    RowBox[{
     RowBox[{"FindFit", "[", 
      RowBox[{
       RowBox[{"Table", "[", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{
           RowBox[{"omega", "[", 
            RowBox[{"[", "i", "]"}], "]"}], ",", 
           RowBox[{"Log10", "[", 
            RowBox[{"data", "[", 
             RowBox[{"[", 
              RowBox[{"j", ",", "i"}], "]"}], "]"}], "]"}]}], "}"}], ",", 
         RowBox[{"{", 
          RowBox[{"i", ",", "lim", ",", "nbener"}], "}"}]}], "]"}], ",", 
       RowBox[{
        RowBox[{"a1", "*", "x"}], "+", "b1"}], ",", 
       RowBox[{"{", 
        RowBox[{"a1", ",", "b1"}], "}"}], ",", "x"}], "]"}], ",", 
     RowBox[{"{", 
      RowBox[{"j", ",", "lima", ",", "nba"}], "}"}]}], "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"a", " ", "=", " ", 
   RowBox[{"Table", "[", 
    RowBox[{"0", ",", 
     RowBox[{"{", 
      RowBox[{"j", ",", "1", ",", "nba"}], "}"}]}], "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"b", " ", "=", " ", 
   RowBox[{"Table", "[", 
    RowBox[{"0", ",", 
     RowBox[{"{", 
      RowBox[{"j", ",", "1", ",", "nba"}], "}"}]}], "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{
   RowBox[{"For", "[", 
    RowBox[{
     RowBox[{"j", "=", "lima"}], ",", 
     RowBox[{"j", "<", 
      RowBox[{"nba", "+", "1"}]}], ",", 
     RowBox[{"j", "++"}], ",", 
     RowBox[{
      RowBox[{"{", 
       RowBox[{
        RowBox[{"a", "[", 
         RowBox[{"[", "j", "]"}], "]"}], ",", 
        RowBox[{"b", "[", 
         RowBox[{"[", "j", "]"}], "]"}]}], "}"}], "=", 
      RowBox[{
       RowBox[{"{", 
        RowBox[{"a1", ",", "b1"}], "}"}], "/.", 
       RowBox[{"f", "[", 
        RowBox[{"[", 
         RowBox[{"j", "-", "lima", "+", "1"}], "]"}], "]"}]}]}]}], "]"}], 
   ";"}], "\[IndentingNewLine]", 
  RowBox[{"(*", 
   RowBox[{"ListPlot", "[", 
    RowBox[{
     RowBox[{"{", 
      RowBox[{
       RowBox[{"Table", "[", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{
           RowBox[{"omega", "[", 
            RowBox[{"[", "i", "]"}], "]"}], ",", 
           RowBox[{"Log10", "[", 
            RowBox[{"data", "[", 
             RowBox[{"[", 
              RowBox[{"index", ",", "i"}], "]"}], "]"}], "]"}]}], "}"}], ",", 
         RowBox[{"{", 
          RowBox[{"i", ",", "lim", ",", "nbener"}], "}"}]}], "]"}], ",", 
       RowBox[{"Table", "[", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{
           RowBox[{"omega", "[", 
            RowBox[{"[", "i", "]"}], "]"}], ",", 
           RowBox[{
            RowBox[{
             RowBox[{"a", "[", 
              RowBox[{"[", "index", "]"}], "]"}], "*", 
             RowBox[{"omega", "[", 
              RowBox[{"[", "i", "]"}], "]"}]}], "+", 
            RowBox[{"b", "[", 
             RowBox[{"[", "index", "]"}], "]"}]}]}], "}"}], ",", 
         RowBox[{"{", 
          RowBox[{"i", ",", "lim", ",", "nbener"}], "}"}]}], "]"}]}], "}"}], 
     ",", 
     RowBox[{"Joined", "\[Rule]", 
      RowBox[{"{", 
       RowBox[{"False", ",", " ", "True"}], "}"}]}]}], "]"}], 
   "*)"}]}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"fbis", "=", 
   RowBox[{"Table", "[", 
    RowBox[{
     RowBox[{"FindFit", "[", 
      RowBox[{
       RowBox[{"Table", "[", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{
           RowBox[{"omega", "[", 
            RowBox[{"[", "i", "]"}], "]"}], ",", 
           RowBox[{
            RowBox[{"Log10", "[", 
             RowBox[{"data", "[", 
              RowBox[{"[", 
               RowBox[{"j", ",", "i"}], "]"}], "]"}], "]"}], "-", 
            RowBox[{"(", 
             RowBox[{
              RowBox[{
               RowBox[{"a", "[", 
                RowBox[{"[", "j", "]"}], "]"}], "*", 
               RowBox[{"omega", "[", 
                RowBox[{"[", "i", "]"}], "]"}]}], "+", 
              RowBox[{"b", "[", 
               RowBox[{"[", "j", "]"}], "]"}]}], ")"}]}]}], "}"}], ",", 
         RowBox[{"{", 
          RowBox[{"i", ",", "lim", ",", "nbener"}], "}"}]}], "]"}], ",", 
       RowBox[{
        RowBox[{"c1", "*", 
         RowBox[{"Cos", "[", 
          RowBox[{"e1", "*", "x"}], "]"}]}], "+", 
        RowBox[{"d1", "*", 
         RowBox[{"Sin", "[", 
          RowBox[{"e1", "*", "x"}], "]"}]}]}], ",", 
       RowBox[{"{", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{"c1", ",", "1"}], "}"}], ",", 
         RowBox[{"{", 
          RowBox[{"d1", ",", "1"}], "}"}], ",", 
         RowBox[{"{", 
          RowBox[{"e1", ",", 
           RowBox[{"2", "*", 
            RowBox[{"Pi", "/", 
             RowBox[{"If", "[", 
              RowBox[{
               RowBox[{
                RowBox[{"aet", "[", 
                 RowBox[{"[", "j", "]"}], "]"}], "\[LessEqual]", "0.5"}], ",",
                "0.8", ",", "1"}], "]"}]}]}]}], "}"}]}], "}"}], ",", "x"}], 
      "]"}], ",", 
     RowBox[{"{", 
      RowBox[{"j", ",", "lima", ",", "nba"}], "}"}]}], "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"c", " ", "=", " ", 
   RowBox[{"Table", "[", 
    RowBox[{"0", ",", 
     RowBox[{"{", 
      RowBox[{"j", ",", "1", ",", "nba"}], "}"}]}], "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"d", " ", "=", " ", 
   RowBox[{"Table", "[", 
    RowBox[{"0", ",", 
     RowBox[{"{", 
      RowBox[{"j", ",", "1", ",", "nba"}], "}"}]}], "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"e", " ", "=", " ", 
   RowBox[{"Table", "[", 
    RowBox[{"0", ",", 
     RowBox[{"{", 
      RowBox[{"j", ",", "1", ",", "nba"}], "}"}]}], "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"j", "=", "lima"}], ",", 
    RowBox[{"j", "<", 
     RowBox[{"nba", "+", "1"}]}], ",", 
    RowBox[{"j", "++"}], ",", 
    RowBox[{
     RowBox[{"{", 
      RowBox[{
       RowBox[{"c", "[", 
        RowBox[{"[", "j", "]"}], "]"}], ",", 
       RowBox[{"d", "[", 
        RowBox[{"[", "j", "]"}], "]"}], ",", 
       RowBox[{"e", "[", 
        RowBox[{"[", "j", "]"}], "]"}]}], "}"}], "=", 
     RowBox[{
      RowBox[{"{", 
       RowBox[{"c1", ",", "d1", ",", "e1"}], "}"}], "/.", 
      RowBox[{"fbis", "[", 
       RowBox[{"[", 
        RowBox[{"j", "-", "lima", "+", "1"}], "]"}], "]"}]}]}]}], "]"}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"(*", 
   RowBox[{"ListPlot", "[", 
    RowBox[{
     RowBox[{"{", 
      RowBox[{
       RowBox[{"Table", "[", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{
           RowBox[{"omega", "[", 
            RowBox[{"[", "i", "]"}], "]"}], ",", 
           RowBox[{
            RowBox[{"Log10", "[", 
             RowBox[{"data", "[", 
              RowBox[{"[", 
               RowBox[{"index", ",", "i"}], "]"}], "]"}], "]"}], "-", 
            RowBox[{"(", 
             RowBox[{
              RowBox[{
               RowBox[{"a", "[", 
                RowBox[{"[", "index", "]"}], "]"}], "*", 
               RowBox[{"omega", "[", 
                RowBox[{"[", "i", "]"}], "]"}]}], "+", 
              RowBox[{"b", "[", 
               RowBox[{"[", "index", "]"}], "]"}]}], ")"}]}]}], "}"}], ",", 
         RowBox[{"{", 
          RowBox[{"i", ",", "lim", ",", "nbener"}], "}"}]}], "]"}], ",", 
       RowBox[{"Table", "[", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{
           RowBox[{"omega", "[", 
            RowBox[{"[", "i", "]"}], "]"}], ",", 
           RowBox[{
            RowBox[{
             RowBox[{"c", "[", 
              RowBox[{"[", "index", "]"}], "]"}], "*", 
             RowBox[{"Cos", "[", 
              RowBox[{
               RowBox[{"e", "[", 
                RowBox[{"[", "index", "]"}], "]"}], "*", 
               RowBox[{"omega", "[", 
                RowBox[{"[", "i", "]"}], "]"}]}], "]"}]}], "+", 
            RowBox[{
             RowBox[{"d", "[", 
              RowBox[{"[", "index", "]"}], "]"}], "*", 
             RowBox[{"Sin", "[", 
              RowBox[{
               RowBox[{"e", "[", 
                RowBox[{"[", "index", "]"}], "]"}], "*", 
               RowBox[{"omega", "[", 
                RowBox[{"[", "i", "]"}], "]"}]}], "]"}]}]}]}], "}"}], ",", 
         RowBox[{"{", 
          RowBox[{"i", ",", "lim", ",", "nbener"}], "}"}]}], "]"}]}], "}"}], 
     ",", 
     RowBox[{"Joined", "\[Rule]", 
      RowBox[{"{", 
       RowBox[{"False", ",", " ", "True"}], "}"}]}]}], "]"}], 
   "*)"}]}]}], "Input",
 CellChangeTimes->{{3.761026189169488*^9, 3.761026274972247*^9}, {
   3.7610263142265196`*^9, 3.761026360008604*^9}, {3.761026409259895*^9, 
   3.761026585709421*^9}, {3.761026806690651*^9, 3.761026835377304*^9}, {
   3.761026891755208*^9, 3.7610269105510426`*^9}, {3.7610271848273535`*^9, 
   3.7610272203472195`*^9}, {3.761030139138275*^9, 3.761030169357731*^9}, {
   3.761547185205516*^9, 3.761547302912145*^9}, {3.7615475072383604`*^9, 
   3.7615476310524025`*^9}, {3.761548025246581*^9, 3.7615480769424815`*^9}, {
   3.7615482098516636`*^9, 3.761548236473513*^9}, {3.7615484297514477`*^9, 
   3.7615484609843063`*^9}, {3.761548507864393*^9, 3.761548536304446*^9}, {
   3.7615485740435157`*^9, 3.7615485794335256`*^9}, {3.761548612712988*^9, 
   3.7615486153251925`*^9}, {3.761548648433654*^9, 3.7615486508078585`*^9}, {
   3.761548682761521*^9, 3.7615486831691217`*^9}, {3.761548739172625*^9, 
   3.761548742270431*^9}, {3.761548783406708*^9, 3.761548792348924*^9}, {
   3.7615488808722906`*^9, 3.761548890782709*^9}, {3.761550326898368*^9, 
   3.7615503335377474`*^9}, {3.761550407308967*^9, 3.7615504077349916`*^9}, {
   3.761550446956235*^9, 3.7615504474932656`*^9}, {3.76155124097665*^9, 
   3.76155124219772*^9}, {3.7615512877843275`*^9, 3.7615513124877405`*^9}, {
   3.761891591753726*^9, 3.76189159926834*^9}, {3.7618916822645035`*^9, 
   3.761891711391358*^9}, {3.761891774794676*^9, 3.761891775623477*^9}, {
   3.7620628819854336`*^9, 3.762062908740287*^9}, {3.762062970522809*^9, 
   3.76206297091381*^9}, {3.7620630081232853`*^9, 3.762063008700486*^9}, {
   3.762063181737693*^9, 3.762063209715952*^9}, {3.762063253679243*^9, 
   3.762063305825548*^9}, {3.76206346637228*^9, 3.7620634667466807`*^9}, 
   3.762063526932401*^9, {3.7621664114941645`*^9, 3.762166442121917*^9}, {
   3.7621667888797503`*^9, 3.7621667895407877`*^9}, {3.765097111436854*^9, 
   3.7650971253514795`*^9}, {3.7650973638289127`*^9, 
   3.7650975595504684`*^9}, {3.7650976132287664`*^9, 
   3.7650977328839855`*^9}, {3.7651033367410407`*^9, 3.7651033613692856`*^9}, 
   3.7651035670038595`*^9, {3.7657966824041867`*^9, 3.765796756820019*^9}, {
   3.7657969099724035`*^9, 3.7657969430262375`*^9}, {3.7657978439937525`*^9, 
   3.765797864701174*^9}, {3.765798007788044*^9, 3.7657980100959606`*^9}, {
   3.765798208477353*^9, 
   3.7657982138746233`*^9}},ExpressionUUID->"1e630913-e032-457a-bc47-\
6e2a25773c7a"],

Cell[BoxData[{
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"j", "=", "1"}], ",", 
    RowBox[{"j", "<", 
     RowBox[{"nba", "+", "1"}]}], ",", 
    RowBox[{"j", "++"}], ",", 
    RowBox[{
     RowBox[{"fits", "[", 
      RowBox[{"[", 
       RowBox[{"j", ",", "3"}], "]"}], "]"}], "=", 
     RowBox[{"a", "[", 
      RowBox[{"[", "j", "]"}], "]"}]}]}], "]"}], ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"j", "=", "1"}], ",", 
    RowBox[{"j", "<", 
     RowBox[{"nba", "+", "1"}]}], ",", 
    RowBox[{"j", "++"}], ",", 
    RowBox[{
     RowBox[{"fits", "[", 
      RowBox[{"[", 
       RowBox[{"j", ",", "4"}], "]"}], "]"}], "=", 
     RowBox[{"b", "[", 
      RowBox[{"[", "j", "]"}], "]"}]}]}], "]"}], ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"j", "=", "1"}], ",", 
    RowBox[{"j", "<", 
     RowBox[{"nba", "+", "1"}]}], ",", 
    RowBox[{"j", "++"}], ",", 
    RowBox[{
     RowBox[{"fits", "[", 
      RowBox[{"[", 
       RowBox[{"j", ",", "5"}], "]"}], "]"}], "=", 
     RowBox[{"c", "[", 
      RowBox[{"[", "j", "]"}], "]"}]}]}], "]"}], ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"j", "=", "1"}], ",", 
    RowBox[{"j", "<", 
     RowBox[{"nba", "+", "1"}]}], ",", 
    RowBox[{"j", "++"}], ",", 
    RowBox[{
     RowBox[{"fits", "[", 
      RowBox[{"[", 
       RowBox[{"j", ",", "6"}], "]"}], "]"}], "=", 
     RowBox[{"d", "[", 
      RowBox[{"[", "j", "]"}], "]"}]}]}], "]"}], ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"j", "=", "1"}], ",", 
    RowBox[{"j", "<", 
     RowBox[{"nba", "+", "1"}]}], ",", 
    RowBox[{"j", "++"}], ",", 
    RowBox[{
     RowBox[{"fits", "[", 
      RowBox[{"[", 
       RowBox[{"j", ",", "7"}], "]"}], "]"}], "=", 
     RowBox[{"e", "[", 
      RowBox[{"[", "j", "]"}], "]"}]}]}], "]"}], ";"}]}], "Input",
 CellChangeTimes->{{3.7615473833115015`*^9, 3.761547451171646*^9}, {
  3.761548746379238*^9, 3.761548748085642*^9}, {3.7632101308204226`*^9, 
  3.7632101350540304`*^9}, {3.765097744737407*^9, 3.765097874775843*^9}, {
  3.7650983319776745`*^9, 3.7650983376786847`*^9}, {3.7650983701431437`*^9, 
  3.7650983776245575`*^9}},ExpressionUUID->"cf71ebcf-de37-4506-b2ef-\
9e771dd1f074"],

Cell[BoxData[
 RowBox[{"(*", 
  RowBox[{"ListPlot", "[", 
   RowBox[{
    RowBox[{"Table", "[", 
     RowBox[{
      RowBox[{"Table", "[", 
       RowBox[{
        RowBox[{"{", 
         RowBox[{
          RowBox[{"omega", "[", 
           RowBox[{"[", "i", "]"}], "]"}], ",", 
          RowBox[{
           RowBox[{
            RowBox[{"fits", "[", 
             RowBox[{"[", 
              RowBox[{"j", ",", "3"}], "]"}], "]"}], "*", 
            RowBox[{"omega", "[", 
             RowBox[{"[", "i", "]"}], "]"}]}], "+", 
           RowBox[{"fits", "[", 
            RowBox[{"[", 
             RowBox[{"j", ",", "4"}], "]"}], "]"}], "+", 
           RowBox[{
            RowBox[{"fits", "[", 
             RowBox[{"[", 
              RowBox[{"j", ",", "5"}], "]"}], "]"}], "*", 
            RowBox[{"Cos", "[", 
             RowBox[{
              RowBox[{"fits", "[", 
               RowBox[{"[", 
                RowBox[{"j", ",", "7"}], "]"}], "]"}], "*", 
              RowBox[{"omega", "[", 
               RowBox[{"[", "i", "]"}], "]"}]}], "]"}]}], "+", 
           RowBox[{
            RowBox[{"fits", "[", 
             RowBox[{"[", 
              RowBox[{"j", ",", "6"}], "]"}], "]"}], "*", 
            RowBox[{"Sin", "[", 
             RowBox[{
              RowBox[{"fits", "[", 
               RowBox[{"[", 
                RowBox[{"j", ",", "7"}], "]"}], "]"}], "*", 
              RowBox[{"omega", "[", 
               RowBox[{"[", "i", "]"}], "]"}]}], "]"}]}]}]}], "}"}], ",", 
        RowBox[{"{", 
         RowBox[{"i", ",", "lim", ",", "nbener"}], "}"}]}], "]"}], ",", 
      RowBox[{"{", 
       RowBox[{"j", ",", "lima", ",", "nba"}], "}"}]}], "]"}], ",", 
    RowBox[{"Joined", "\[Rule]", "True"}]}], "]"}], "*)"}]], "Input",
 CellChangeTimes->{{3.761548960908039*^9, 3.7615490459193993`*^9}, {
  3.7650978890658693`*^9, 3.765097903576296*^9}, {3.765098422070238*^9, 
  3.7650984466078825`*^9}, {3.7657981228533893`*^9, 
  3.7657981236228256`*^9}},ExpressionUUID->"ccac96d9-7ff3-4da4-9977-\
17a0a4f5245e"],

Cell[BoxData[{
 RowBox[{
  RowBox[{"limlow", " ", "=", " ", "5"}], ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"(*", 
   RowBox[{"ListPlot", "[", 
    RowBox[{
     RowBox[{"Table", "[", 
      RowBox[{
       RowBox[{"{", 
        RowBox[{
         RowBox[{"Log10", "[", 
          RowBox[{"omega", "[", 
           RowBox[{"[", "i", "]"}], "]"}], "]"}], ",", 
         RowBox[{"Log10", "[", 
          RowBox[{"data", "[", 
           RowBox[{"[", 
            RowBox[{"index", ",", "i"}], "]"}], "]"}], "]"}]}], "}"}], ",", 
       RowBox[{"{", 
        RowBox[{"i", ",", "1", ",", "limlow"}], "}"}]}], "]"}], ",", 
     RowBox[{"PlotRange", "\[Rule]", "All"}]}], "]"}], "*)"}]}]}], "Input",
 CellChangeTimes->{{3.7610295383999624`*^9, 3.7610295666744146`*^9}, {
   3.7613784690444746`*^9, 3.761378469747476*^9}, {3.76137884108117*^9, 
   3.7613788426129727`*^9}, {3.7615435612278004`*^9, 3.761543561836201*^9}, {
   3.7615438211633368`*^9, 3.7615438216313376`*^9}, {3.7615438528189955`*^9, 
   3.7615438719046307`*^9}, {3.761544087438455*^9, 3.761544128855532*^9}, {
   3.761544165573203*^9, 3.7615441999592676`*^9}, {3.7615442320531273`*^9, 
   3.7615442342245317`*^9}, {3.7615442998948536`*^9, 3.761544300036254*^9}, {
   3.7615443780193987`*^9, 3.761544383643409*^9}, {3.7615444475363274`*^9, 
   3.76154449138181*^9}, {3.761544524744872*^9, 3.7615446258862596`*^9}, {
   3.76154467429455*^9, 3.7615447360996647`*^9}, {3.7615447802205467`*^9, 
   3.7615447806261473`*^9}, {3.7615448117624054`*^9, 3.761544812075406*^9}, {
   3.7615448440152655`*^9, 3.7615448448420672`*^9}, {3.7615454914087615`*^9, 
   3.7615454924237633`*^9}, {3.7615456201430197`*^9, 3.761545628315835*^9}, {
   3.761545706947991*^9, 3.761545707357592*^9}, {3.7615457713793154`*^9, 
   3.7615458141726046`*^9}, {3.7615459131887937`*^9, 
   3.7615459445540648`*^9}, {3.7615493587654266`*^9, 
   3.7615493818476696`*^9}, {3.761549440289179*^9, 3.7615494407259793`*^9}, {
   3.761549510053708*^9, 3.761549603344881*^9}, 3.7615496597211857`*^9, {
   3.761550079133197*^9, 3.761550164252065*^9}, {3.7615502001371174`*^9, 
   3.761550200515139*^9}, {3.7615502363561893`*^9, 3.7615502368162155`*^9}, {
   3.761551062537444*^9, 3.76155114096793*^9}, {3.761551183750377*^9, 
   3.7615511842744074`*^9}, {3.761557988870343*^9, 3.761557995650756*^9}, {
   3.762064144646109*^9, 3.7620641971976137`*^9}, {3.762064281483782*^9, 
   3.7620643658545537`*^9}, {3.7620645654788156`*^9, 
   3.7620645658688164`*^9}, {3.76206459816608*^9, 3.762064699336094*^9}, {
   3.7620647692718363`*^9, 3.762064819314737*^9}, {3.7620648516592016`*^9, 
   3.762065012117985*^9}, {3.7620651730223055`*^9, 3.762065208322577*^9}, {
   3.762065391859546*^9, 3.7620653923305473`*^9}, {3.7620654339854307`*^9, 
   3.7620655548254757`*^9}, 3.762065611210644*^9, {3.762066903687765*^9, 
   3.762066904015366*^9}, {3.762167185477434*^9, 3.7621671858694563`*^9}, {
   3.762167400682743*^9, 3.762167401081766*^9}, {3.765098074887007*^9, 
   3.765098115646081*^9}, {3.76510346833288*^9, 3.765103469082681*^9}, {
   3.76579751822244*^9, 3.7657975187540646`*^9}, {3.7657980221433563`*^9, 
   3.7657980228077865`*^9}, {3.765798060904391*^9, 3.7657980799090676`*^9}, {
   3.7657982230646787`*^9, 3.7657982297458067`*^9}, {3.7657983157814016`*^9, 
   3.765798317701296*^9}, {3.7657983714483724`*^9, 
   3.765798374237114*^9}},ExpressionUUID->"9f81471e-6820-42da-a957-\
646a9d17c1b0"],

Cell[BoxData[{
 RowBox[{
  RowBox[{"g", "=", 
   RowBox[{"Table", "[", 
    RowBox[{
     RowBox[{"FindFit", "[", 
      RowBox[{
       RowBox[{"Table", "[", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{
           RowBox[{"Log10", "[", 
            RowBox[{"omega", "[", 
             RowBox[{"[", "i", "]"}], "]"}], "]"}], ",", 
           RowBox[{"Log10", "[", 
            RowBox[{"data", "[", 
             RowBox[{"[", 
              RowBox[{"j", ",", "i"}], "]"}], "]"}], "]"}]}], "}"}], ",", 
         RowBox[{"{", 
          RowBox[{"i", ",", "1", ",", "limlow"}], "}"}]}], "]"}], ",", 
       RowBox[{
        RowBox[{"a1", "*", "x"}], "+", "b1"}], ",", 
       RowBox[{"{", 
        RowBox[{"a1", ",", "b1"}], "}"}], ",", "x"}], "]"}], ",", 
     RowBox[{"{", 
      RowBox[{"j", ",", "lima", ",", "nba"}], "}"}]}], "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"alow", " ", "=", " ", 
   RowBox[{"Table", "[", 
    RowBox[{"0", ",", 
     RowBox[{"{", 
      RowBox[{"j", ",", "1", ",", "nba"}], "}"}]}], "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"blow", " ", "=", " ", 
   RowBox[{"Table", "[", 
    RowBox[{"0", ",", 
     RowBox[{"{", 
      RowBox[{"j", ",", "1", ",", "nba"}], "}"}]}], "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"j", "=", "lima"}], ",", 
    RowBox[{"j", "<", 
     RowBox[{"nba", "+", "1"}]}], ",", 
    RowBox[{"j", "++"}], ",", 
    RowBox[{
     RowBox[{"{", 
      RowBox[{
       RowBox[{"alow", "[", 
        RowBox[{"[", "j", "]"}], "]"}], ",", 
       RowBox[{"blow", "[", 
        RowBox[{"[", "j", "]"}], "]"}]}], "}"}], "=", 
     RowBox[{
      RowBox[{"{", 
       RowBox[{"a1", ",", "b1"}], "}"}], "/.", 
      RowBox[{"g", "[", 
       RowBox[{"[", 
        RowBox[{"j", "-", "lima", "+", "1"}], "]"}], "]"}]}]}]}], "]"}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"(*", 
   RowBox[{"ListPlot", "[", 
    RowBox[{
     RowBox[{"{", 
      RowBox[{
       RowBox[{"Table", "[", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{
           RowBox[{"Log10", "[", 
            RowBox[{"omega", "[", 
             RowBox[{"[", "i", "]"}], "]"}], "]"}], ",", 
           RowBox[{"Log10", "[", 
            RowBox[{"data", "[", 
             RowBox[{"[", 
              RowBox[{"index", ",", "i"}], "]"}], "]"}], "]"}]}], "}"}], ",", 
         RowBox[{"{", 
          RowBox[{"i", ",", "1", ",", "limlow"}], "}"}]}], "]"}], ",", 
       RowBox[{"Table", "[", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{
           RowBox[{"Log10", "[", 
            RowBox[{"omega", "[", 
             RowBox[{"[", "i", "]"}], "]"}], "]"}], ",", 
           RowBox[{
            RowBox[{
             RowBox[{"alow", "[", 
              RowBox[{"[", "index", "]"}], "]"}], "*", 
             RowBox[{"Log10", "[", 
              RowBox[{"omega", "[", 
               RowBox[{"[", "i", "]"}], "]"}], "]"}]}], "+", 
            RowBox[{"blow", "[", 
             RowBox[{"[", "index", "]"}], "]"}]}]}], "}"}], ",", 
         RowBox[{"{", 
          RowBox[{"i", ",", "1", ",", "limlow"}], "}"}]}], "]"}]}], "}"}], 
     ",", 
     RowBox[{"Joined", "\[Rule]", 
      RowBox[{"{", 
       RowBox[{"False", ",", "True"}], "}"}]}], ",", 
     RowBox[{"PlotRange", "\[Rule]", "All"}]}], "]"}], "*)"}]}]}], "Input",
 CellChangeTimes->CompressedData["
1:eJwd0GtIk3EYBfClafMy0yFpGonmitFEnFNc4vQVHXSlpoYYSS6NNFGD1DAK
FS8T8dKyYUuUIDclKVM00Gl5myWTykbiLS/oCnmbonOKpPae/4eH35dzzofH
V54tS7NhsVjhzMHOmdzwejua6hgtOw8bioOrobpqrxbG9P05gLxIEesFo6HF
UhNtT1M5/MBaaF5TPIcGo0c9fHucNwyTG5KIdRKL8BmjJOJECHTVseOgV2cW
8duX9BG4nbGih+NZgRNQvxlnhPuue0uw9LPaBO8mNZ2qYxwLjhfA+FfeIvja
VnIOTrL8JNB+W0x85x8rhZlF3vGw5OMFBxXyMo0r5AdJPeHs2UfEkAZtACxQ
dRFrvUzJUGBQyqG12ZIB59dY96ApqCgbKuRV92HManc+rJcOEt/vJs6RPVMG
sd0jehEWfkheglFtk6uwrdRMXL5oilAzVut8JHDQzZj29AhN7V5eJA4tadOh
k6mTSKdkFUJrUEQZzO4vUEKLLKEOXvk9p4HccrYWum9VDEP/w/t6uDNu9xUa
8pTE4hb9D5jHsxJTNgZWoaIq6i8c9lFtwsbbrVukLyz4B0WiSuLExoKtkrGk
XOAI/T7ZukBFl9wN/hw64wU10yk+UG993Auv2wh1MEyx3jjE+ODmoSZ456C7
A1pOrhOLKxsN8JKmlUixpYkDDsw/H2YSd8T5t2BTzwjRT6Y1zzvS1K/YKaIs
dGUL6iOFVjgapnNfYLSkNnvAN558MTzNzSVm9oZSkNsnJU4nODtynGjqxlQk
keb7+UIjT0AcuxpQDmc54gr4sveJSsChKVZPP1FxNOd7EmM05bwMzVxP53YX
mjpWc8CBJakzKthvNanhhh27DUrKrhH/A16pwp0=
  "],ExpressionUUID->"e9618ec2-4845-48a6-9ff2-8ad5a1118aac"],

Cell[BoxData[{
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"j", "=", "1"}], ",", 
    RowBox[{"j", "<", 
     RowBox[{"nba", "+", "1"}]}], ",", 
    RowBox[{"j", "++"}], ",", 
    RowBox[{
     RowBox[{"fits", "[", 
      RowBox[{"[", 
       RowBox[{"j", ",", "1"}], "]"}], "]"}], "=", 
     RowBox[{"alow", "[", 
      RowBox[{"[", "j", "]"}], "]"}]}]}], "]"}], ";"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"For", "[", 
   RowBox[{
    RowBox[{"j", "=", "1"}], ",", 
    RowBox[{"j", "<", 
     RowBox[{"nba", "+", "1"}]}], ",", 
    RowBox[{"j", "++"}], ",", 
    RowBox[{
     RowBox[{"fits", "[", 
      RowBox[{"[", 
       RowBox[{"j", ",", "2"}], "]"}], "]"}], "=", 
     RowBox[{"blow", "[", 
      RowBox[{"[", "j", "]"}], "]"}]}]}], "]"}], ";"}]}], "Input",
 CellChangeTimes->{{3.7615437789622583`*^9, 3.7615438033579035`*^9}, {
  3.761543847319785*^9, 3.7615438486311874`*^9}, {3.761544764966918*^9, 
  3.7615447662345204`*^9}, {3.761545687070754*^9, 3.7615456901177597`*^9}, {
  3.7650982790979786`*^9, 3.7650983168452473`*^9}, {3.765098358572523*^9, 
  3.7650983610559273`*^9}, {3.7651035896031003`*^9, 
  3.76510359494331*^9}},ExpressionUUID->"d92151d4-168c-4780-b703-\
d5450f84285a"],

Cell[BoxData[
 RowBox[{"(*", 
  RowBox[{"ListPlot", "[", 
   RowBox[{
    RowBox[{"Table", "[", 
     RowBox[{
      RowBox[{"Table", "[", 
       RowBox[{
        RowBox[{"{", 
         RowBox[{
          RowBox[{"Log10", "[", 
           RowBox[{"omega", "[", 
            RowBox[{"[", "i", "]"}], "]"}], "]"}], ",", 
          RowBox[{
           RowBox[{
            RowBox[{"fits", "[", 
             RowBox[{"[", 
              RowBox[{"j", ",", "1"}], "]"}], "]"}], "*", 
            RowBox[{"Log10", "[", 
             RowBox[{"omega", "[", 
              RowBox[{"[", "i", "]"}], "]"}], "]"}]}], "+", 
           RowBox[{"fits", "[", 
            RowBox[{"[", 
             RowBox[{"j", ",", "2"}], "]"}], "]"}]}]}], "}"}], ",", 
        RowBox[{"{", 
         RowBox[{"i", ",", "1", ",", "limlow"}], "}"}]}], "]"}], ",", 
      RowBox[{"{", 
       RowBox[{"j", ",", "lima", ",", "nba"}], "}"}]}], "]"}], ",", 
    RowBox[{"Joined", "\[Rule]", "True"}]}], "]"}], "*)"}]], "Input",
 CellChangeTimes->{{3.761545951353077*^9, 3.761545953914482*^9}, {
   3.7615496291907296`*^9, 3.761549640814151*^9}, 3.761550156617628*^9, {
   3.765098402844603*^9, 3.7650984056722083`*^9}, {3.7650984586339045`*^9, 
   3.765098465583917*^9}, {3.7657981167426863`*^9, 
   3.7657981177971387`*^9}},ExpressionUUID->"f7f29ae0-13a4-4bc3-8e42-\
81e76e09b896"],

Cell[BoxData[{
 RowBox[{
  RowBox[{"file", "=", 
   RowBox[{"OpenWrite", "[", "\"\<fits0.5_fM.txt\>\"", "]"}]}], 
  ";"}], "\[IndentingNewLine]", 
 RowBox[{"For", "[", 
  RowBox[{
   RowBox[{"i", "=", "1"}], ",", 
   RowBox[{"i", "<", 
    RowBox[{"nba", "+", "1"}]}], ",", 
   RowBox[{"i", "++"}], ",", 
   RowBox[{"WriteLine", "[", 
    RowBox[{"file", ",", 
     RowBox[{"ExportString", "[", 
      RowBox[{
       RowBox[{"{", 
        RowBox[{"fits", "[", 
         RowBox[{"[", "i", "]"}], "]"}], "}"}], ",", "\"\<TSV\>\""}], "]"}]}],
     "]"}]}], "]"}], "\[IndentingNewLine]", 
 RowBox[{
  RowBox[{"Close", "[", "file", "]"}], ";"}]}], "Input",
 CellChangeTimes->CompressedData["
1:eJxTTMoPSmViYGAQA2IQncd4J3EK2xvHLF7OTBB9udRpDYg+wtK/HkQbyHsa
TAPSZ6fFgmm2+OIqEF2T2QSmS5kL3oFotbd1YLqhaXvORPY3ju92KeWDaKu5
gj0g+oCkFJi+0B6zAEQH6y4E0+uM69aB6L/b28F0wwuNnWD1TKVgWvViyCEQ
fSo5GUzzLG49A6KzTt0E0w2NE+0nAWmN3wvA9Hqvqf+jeN84JqndY4gG0iWb
rAtjgfQC0VVgOs5cjn8D3xtHiSBVMO3x7aI5iHaJ+AGmQ5n/rQHRCr5Ma0F0
k2jSYRC973s/mC5ZN/cyiGY7vBhMh7LevgOiO2KfgGkBFSu3jUD6EusBMA0A
VgSkyQ==
  "],ExpressionUUID->"194f317c-a6b7-400f-92fd-38c7e5ede497"]
},
WindowSize->{1904, 998},
WindowMargins->{{-8, Automatic}, {Automatic, 0}},
Magnification:>0.9 Inherited,
FrontEndVersion->"11.1 for Microsoft Windows (64-bit) (April 18, 2017)",
StyleDefinitions->"Default.nb"
]
(* End of Notebook Content *)

(* Internal cache information *)
(*CellTagsOutline
CellTagsIndex->{}
*)
(*CellTagsIndex
CellTagsIndex->{}
*)
(*NotebookFileOutline
Notebook[{
Cell[558, 20, 220, 5, 44, "Input", "ExpressionUUID" -> \
"49eafb7e-4cf2-44f9-89e0-8d2b18b527fc"],
Cell[781, 27, 7921, 175, 360, "Input", "ExpressionUUID" -> \
"0eac3288-2c71-455c-b35c-45c567e7796d"],
Cell[8705, 204, 2858, 53, 83, "Input", "ExpressionUUID" -> \
"5a69311c-b9be-493b-ac63-954c41c134e7"],
Cell[11566, 259, 10878, 292, 380, "Input", "ExpressionUUID" -> \
"1e630913-e032-457a-bc47-6e2a25773c7a"],
Cell[22447, 553, 2330, 71, 184, "Input", "ExpressionUUID" -> \
"cf71ebcf-de37-4506-b2ef-9e771dd1f074"],
Cell[24780, 626, 2030, 53, 28, "Input", "ExpressionUUID" -> \
"ccac96d9-7ff3-4da4-9977-17a0a4f5245e"],
Cell[26813, 681, 3417, 58, 47, "Input", "ExpressionUUID" -> \
"9f81471e-6820-42da-a957-646a9d17c1b0"],
Cell[30233, 741, 4375, 120, 169, "Input", "ExpressionUUID" -> \
"e9618ec2-4845-48a6-9ff2-8ad5a1118aac"],
Cell[34611, 863, 1218, 33, 79, "Input", "ExpressionUUID" -> \
"d92151d4-168c-4780-b703-d5450f84285a"],
Cell[35832, 898, 1345, 34, 28, "Input", "ExpressionUUID" -> \
"f7f29ae0-13a4-4bc3-8e42-81e76e09b896"],
Cell[37180, 934, 1125, 29, 171, "Input", "ExpressionUUID" -> \
"194f317c-a6b7-400f-92fd-38c7e5ede497"]
}
]
*)

