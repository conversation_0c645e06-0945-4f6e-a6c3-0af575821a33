##
# This script computes the high and low fitting parameters for higher-dimensional BHs. The "blah" mentions should be replaced with the correct path to your tables/figures.


import numpy as np
import scipy.interpolate as scp
import scipy.integrate as sci
import pylab
from scipy.special import gamma
from scipy import stats

## Exploiting higher-dimensional results
# greybody factors for higher-dimensional BHs
data_0 = np.genfromtxt("blah")
data_1 = np.genfromtxt("blah")
data_2 = np.genfromtxt("blah")
data_12 = np.genfromtxt("blah")

# comparative greybody factors for Schwarzschild BHs
data_0_comp = np.genfromtxt("blah")
data_1_comp = np.genfromtxt("blah")
data_2_comp = np.genfromtxt("blah")
data_12_comp = np.genfromtxt("blah")


# data at low energy, you can produce those by modifying the energy bounds in the Mathematica scripts
data_0_low = np.genfromtxt("blah")
data_1_low = np.genfromtxt("blah")
data_2_low = np.genfromtxt("blah")
data_12_low = np.genfromtxt("blah")

data_0_comp_low = np.genfromtxt("blah")
data_1_comp_low = np.genfromtxt("blah")
data_2_comp_low = np.genfromtxt("blah")
data_12_comp_low = np.genfromtxt("blah")

# be careful to reproduce the energy range you have used for the computations
enumber = len(data_0[0])
energies = np.zeros(enumber)
Emin = 0.01
Einter = 1.
Emax = 5.
for i in range(len(energies)):
    if i<100:
        energies[i] = 10**(np.log10(Emin) + (np.log10(Einter) - np.log10(Emin))/(100.)*i)
    else:
        energies[i] = Einter + (Emax - Einter)/99.*(i-100.)

energies_low = np.zeros(50)
for i in range(50):
    energies_low[i] = 10**(-6 + (-4 - (-6))/(50 - 1)*i)

index_min = 25
index_max = 150

M = 0.5

Mstar = 1

def rH(M,Mstar,n):
    return 1/(np.sqrt(np.pi)*Mstar)*(M/Mstar)**(1/(n+1))*(8*gamma((n+3)/2)/(n+2))**(1/(n+1))

def T(M,Mstar,n):
    return (n+1)/(4*np.pi*rH(M,Mstar,n))

## spin 0
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_0)$")
ax.scatter(np.log10(energies_low),np.log10(data_0_comp_low*(np.exp(energies_low/T(M,Mstar,0))-1.)),s = 1,color = "black",label = "$n = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[1,:]*(np.exp(energies_low/T(M,Mstar,1))-1.)),s = 1,color = "blue",label = "$n = 1$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[2,:]*(np.exp(energies_low/T(M,Mstar,2))-1.)),s = 1,color = "green",label = "$n = 2$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[3,:]*(np.exp(energies_low/T(M,Mstar,3))-1.)),s = 1,color = "red",label = "$n = 3$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[4,:]*(np.exp(energies_low/T(M,Mstar,4))-1.)),s = 1,color = "purple",label = "$n = 4$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[5,:]*(np.exp(energies_low/T(M,Mstar,5))-1.)),s = 1,color = "yellow",label = "$n = 5$")
ax.scatter(np.log10(energies_low),np.log10(data_0_low[6,:]*(np.exp(energies_low/T(M,Mstar,6))-1.)),s = 1,color = "orange",label = "$n = 6$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_0_comp_low*(np.exp(energies_low/T(M,Mstar,0))-1.)))

slopes = np.zeros(6)
intercepts = np.zeros(6)

for i in range(0,6):
    result = stats.linregress(np.log10(energies_low),np.log10(data_0_low[i+1,:]*(np.exp(energies_low/T(M,Mstar,i+1))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 0.5,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[1]*np.log10(energies_low)+intercepts[1],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[2]*np.log10(energies_low)+intercepts[2],linewidth = 0.5,color = "red")
ax.plot(np.log10(energies_low),slopes[3]*np.log10(energies_low)+intercepts[3],linewidth = 0.5,color = "purple")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "yellow")
ax.plot(np.log10(energies_low),slopes[5]*np.log10(energies_low)+intercepts[5],linewidth = 0.5,color = "orange")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_0$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_0_comp[index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "$n = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_0[1,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,1))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$n = 1$")
ax.scatter(energies[index_max:],data_0[2,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,2))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$n = 2$")
ax.scatter(energies[index_max:],data_0[3,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,3))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$n = 3$")
ax.scatter(energies[index_max:],data_0[4,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,4))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "purple",label = "$n = 4$")
ax.scatter(energies[index_max:],data_0[5,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,5))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "yellow",label = "$n = 5$")
ax.scatter(energies[index_max:],data_0[6,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,6))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "orange",label = "$n = 6$")

limit = 0.
for i in range(index_max,len(data_0[0])):
    limit += data_0_comp[i]*(np.exp(energies[i]/T(M,Mstar,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_0[0])-index_max)

limits = np.zeros(6)
for j in range(6):
    for i in range(index_max,len(data_0[0])):
        limits[j] += data_0[1+j,i]*(np.exp(energies[i]/T(M,Mstar,j+1))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_0[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[1],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[2],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")
ax.axhline(y = limits[3],xmin = 0,xmax = 1,linewidth = 0.5,color = "purple")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "yellow")
ax.axhline(y = limits[5],xmin = 0,xmax = 1,linewidth = 0.5,color = "orange")

ax.legend(loc = "best")
f.show()

spin_0_path = "blah"

print("%15s%15s%15s%15s\n"%("n/fits","a1","b1","limit"),file=open(spin_0_path,"w"),end = "")
print("%15i%15.5e%15.5e%15.5e\n"%(0,slope,intercept,limit),file=open(spin_0_path,"a"),end = "")
for i in range(6):
    print("%15i%15.5e%15.5e%15.5e\n"%(i+1,slopes[i],intercepts[i],limits[i]),file=open(spin_0_path,"a"),end = "")

## spin 1
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_0)$")
ax.scatter(np.log10(energies_low),np.log10(data_1_comp_low*(np.exp(energies_low/T(M,Mstar,0))-1.)),s = 1,color = "black",label = "$n = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[1,:]*(np.exp(energies_low/T(M,Mstar,1))-1.)),s = 1,color = "blue",label = "$n = 1$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[2,:]*(np.exp(energies_low/T(M,Mstar,2))-1.)),s = 1,color = "green",label = "$n = 2$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[3,:]*(np.exp(energies_low/T(M,Mstar,3))-1.)),s = 1,color = "red",label = "$n = 3$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[4,:]*(np.exp(energies_low/T(M,Mstar,4))-1.)),s = 1,color = "purple",label = "$n = 4$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[5,:]*(np.exp(energies_low/T(M,Mstar,5))-1.)),s = 1,color = "yellow",label = "$n = 5$")
ax.scatter(np.log10(energies_low),np.log10(data_1_low[6,:]*(np.exp(energies_low/T(M,Mstar,6))-1.)),s = 1,color = "orange",label = "$n = 6$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_1_comp_low*(np.exp(energies_low/T(M,Mstar,0))-1.)))

slopes = np.zeros(6)
intercepts = np.zeros(6)

for i in range(0,6):
    result = stats.linregress(np.log10(energies_low),np.log10(data_1_low[i+1,:]*(np.exp(energies_low/T(M,Mstar,i+1))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 2,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 2,color = "blue")
ax.plot(np.log10(energies_low),slopes[1]*np.log10(energies_low)+intercepts[1],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[2]*np.log10(energies_low)+intercepts[2],linewidth = 0.5,color = "red")
ax.plot(np.log10(energies_low),slopes[3]*np.log10(energies_low)+intercepts[3],linewidth = 0.5,color = "purple")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "yellow")
ax.plot(np.log10(energies_low),slopes[5]*np.log10(energies_low)+intercepts[5],linewidth = 0.5,color = "orange")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_1$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_1_comp[index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "$n = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_1[1,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,1))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$n = 1$")
ax.scatter(energies[index_max:],data_1[2,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,2))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$n = 2$")
ax.scatter(energies[index_max:],data_1[3,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,3))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$n = 3$")
ax.scatter(energies[index_max:],data_1[4,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,4))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "purple",label = "$n = 4$")
ax.scatter(energies[index_max:],data_1[5,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,5))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "yellow",label = "$n = 5$")
ax.scatter(energies[index_max:],data_1[6,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,6))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "orange",label = "$n = 6$")

limit = 0.
for i in range(index_max,len(data_1[0])):
    limit += data_1_comp[i]*(np.exp(energies[i]/T(M,Mstar,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_1[0])-index_max)

limits = np.zeros(6)
for j in range(6):
    for i in range(index_max,len(data_1[0])):
        limits[j] += data_1[1+j,i]*(np.exp(energies[i]/T(M,Mstar,j+1))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_1[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[1],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[2],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")
ax.axhline(y = limits[3],xmin = 0,xmax = 1,linewidth = 0.5,color = "purple")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "yellow")
ax.axhline(y = limits[5],xmin = 0,xmax = 1,linewidth = 0.5,color = "orange")

ax.legend(loc = "best")
f.show()

spin_1_path = "blah"

print("%15s%15s%15s%15s\n"%("n/fits","a1","b1","limit"),file=open(spin_1_path,"w"),end = "")
print("%15i%15.5e%15.5e%15.5e\n"%(0,slope,intercept,limit),file=open(spin_1_path,"a"),end = "")
for i in range(6):
    print("%15i%15.5e%15.5e%15.5e\n"%(i+1,slopes[i],intercepts[i],limits[i]),file=open(spin_1_path,"a"),end = "")

## spin 2
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_2)$")
ax.scatter(np.log10(energies_low),np.log10(data_2_comp_low*(np.exp(energies_low/T(M,Mstar,0))-1.)),s = 1,color = "black",label = "$n = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[1,:]*(np.exp(energies_low/T(M,Mstar,1))-1.)),s = 1,color = "blue",label = "$n = 1$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[2,:]*(np.exp(energies_low/T(M,Mstar,2))-1.)),s = 1,color = "green",label = "$n = 2$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[3,:]*(np.exp(energies_low/T(M,Mstar,3))-1.)),s = 1,color = "red",label = "$n = 3$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[4,:]*(np.exp(energies_low/T(M,Mstar,4))-1.)),s = 1,color = "purple",label = "$n = 4$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[5,:]*(np.exp(energies_low/T(M,Mstar,5))-1.)),s = 1,color = "yellow",label = "$n = 5$")
ax.scatter(np.log10(energies_low),np.log10(data_2_low[6,:]*(np.exp(energies_low/T(M,Mstar,6))-1.)),s = 1,color = "orange",label = "$n = 6$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_2_comp_low*(np.exp(energies_low/T(M,Mstar,0))-1.)))

slopes = np.zeros(6)
intercepts = np.zeros(6)

for i in range(0,6):
    result = stats.linregress(np.log10(energies_low),np.log10(data_2_low[i+1,:]*(np.exp(energies_low/T(M,Mstar,i+1))-1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 2,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 2,color = "blue")
ax.plot(np.log10(energies_low),slopes[1]*np.log10(energies_low)+intercepts[1],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[2]*np.log10(energies_low)+intercepts[2],linewidth = 0.5,color = "red")
ax.plot(np.log10(energies_low),slopes[3]*np.log10(energies_low)+intercepts[3],linewidth = 0.5,color = "purple")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "yellow")
ax.plot(np.log10(energies_low),slopes[5]*np.log10(energies_low)+intercepts[5],linewidth = 0.5,color = "orange")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_2$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_2_comp[index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,0))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "$n = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_2[1,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,1))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$n = 1$")
ax.scatter(energies[index_max:],data_2[2,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,2))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$n = 2$")
ax.scatter(energies[index_max:],data_2[3,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,3))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$n = 3$")
ax.scatter(energies[index_max:],data_2[4,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,4))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "purple",label = "$n = 4$")
ax.scatter(energies[index_max:],data_2[5,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,5))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "yellow",label = "$n = 5$")
ax.scatter(energies[index_max:],data_2[6,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,6))-1.)/(27./4.*energies[index_max:]**2),s = 1,color = "orange",label = "$n = 6$")

limit = 0.
for i in range(index_max,len(data_2[0])):
    limit += data_2_comp[i]*(np.exp(energies[i]/T(M,Mstar,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_2[0])-index_max)

limits = np.zeros(6)
for j in range(6):
    for i in range(index_max,len(data_2[0])):
        limits[j] += data_2[1+j,i]*(np.exp(energies[i]/T(M,Mstar,j+1))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_2[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[1],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[2],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")
ax.axhline(y = limits[3],xmin = 0,xmax = 1,linewidth = 0.5,color = "purple")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "yellow")
ax.axhline(y = limits[5],xmin = 0,xmax = 1,linewidth = 0.5,color = "orange")

ax.legend(loc = "best")
f.show()

spin_2_path = "blah"

print("%15s%15s%15s%15s\n"%("n/fits","a1","b1","limit"),file=open(spin_2_path,"w"),end = "")
print("%15i%15.5e%15.5e%15.5e\n"%(0,slope,intercept,limit),file=open(spin_2_path,"a"),end = "")
for i in range(6):
    print("%15i%15.5e%15.5e%15.5e\n"%(i+1,slopes[i],intercepts[i],limits[i]),file=open(spin_2_path,"a"),end = "")

## spin 1/2 low
fig_width_pt = 80*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 11,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 11,
          'legend.fontsize': 11,
          'xtick.labelsize': 11,
          'ytick.labelsize': 11,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(0)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlim(-6,-4)
#ax.set_ylim(-,0)
ax.set_xlabel("$\log_{10}(x)$")
ax.set_ylabel("$\log_{10}(Q_{1/2})$")
ax.scatter(np.log10(energies_low),np.log10(data_12_comp_low*(np.exp(energies_low/T(M,Mstar,0))+1.)),s = 1,color = "black",label = "$n = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[1,:]*(np.exp(energies_low/T(M,Mstar,1))+1.)),s = 1,color = "blue",label = "$n = 1$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[2,:]*(np.exp(energies_low/T(M,Mstar,2))+1.)),s = 1,color = "green",label = "$n = 2$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[3,:]*(np.exp(energies_low/T(M,Mstar,3))+1.)),s = 1,color = "red",label = "$n = 3$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[4,:]*(np.exp(energies_low/T(M,Mstar,4))+1.)),s = 1,color = "purple",label = "$n = 4$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[5,:]*(np.exp(energies_low/T(M,Mstar,5))+1.)),s = 1,color = "yellow",label = "$n = 5$")
ax.scatter(np.log10(energies_low),np.log10(data_12_low[6,:]*(np.exp(energies_low/T(M,Mstar,6))+1.)),s = 1,color = "orange",label = "$n = 6$")

slope, intercept, r_value, p_value, stderr = stats.linregress(np.log10(energies_low),np.log10(data_12_comp_low*(np.exp(energies_low/T(M,Mstar,0))+1.)))

slopes = np.zeros(6)
intercepts = np.zeros(6)

for i in range(0,6):
    result = stats.linregress(np.log10(energies_low),np.log10(data_12_low[i+1,:]*(np.exp(energies_low/T(M,Mstar,i+1))+1.)))
    slopes[i] = result.slope
    intercepts[i] = result.intercept

ax.plot(np.log10(energies_low),slope*np.log10(energies_low)+intercept,linewidth = 0.5,color = "black")
ax.plot(np.log10(energies_low),slopes[0]*np.log10(energies_low)+intercepts[0],linewidth = 0.5,color = "blue")
ax.plot(np.log10(energies_low),slopes[1]*np.log10(energies_low)+intercepts[1],linewidth = 0.5,color = "green")
ax.plot(np.log10(energies_low),slopes[2]*np.log10(energies_low)+intercepts[2],linewidth = 0.5,color = "red")
ax.plot(np.log10(energies_low),slopes[3]*np.log10(energies_low)+intercepts[3],linewidth = 0.5,color = "purple")
ax.plot(np.log10(energies_low),slopes[4]*np.log10(energies_low)+intercepts[4],linewidth = 0.5,color = "yellow")
ax.plot(np.log10(energies_low),slopes[5]*np.log10(energies_low)+intercepts[5],linewidth = 0.5,color = "orange")

ax.legend(loc = "best")
f.show()

f = pylab.figure(1)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("linear")
ax.set_xlabel("$x$")
ax.set_ylabel("$Q_{1/2}$")
#ax.set_xlim(0,5)
#ax.set_ylim(0,1.5)
ax.scatter(energies[index_max:],data_12_comp[index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,0)) + 1.)/(27./4.*energies[index_max:]**2),s = 1,color = "black",label = "$n = 0\,\,{\\rm (Schwarzschild)}$")
ax.scatter(energies[index_max:],data_12[1,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,1)) + 1.)/(27./4.*energies[index_max:]**2),s = 1,color = "blue",label = "$n = 1$")
ax.scatter(energies[index_max:],data_12[2,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,2)) + 1.)/(27./4.*energies[index_max:]**2),s = 1,color = "green",label = "$n = 2$")
ax.scatter(energies[index_max:],data_12[3,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,3)) + 1.)/(27./4.*energies[index_max:]**2),s = 1,color = "red",label = "$n = 3$")
ax.scatter(energies[index_max:],data_12[4,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,4)) + 1.)/(27./4.*energies[index_max:]**2),s = 1,color = "purple",label = "$n = 4$")
ax.scatter(energies[index_max:],data_12[5,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,5)) + 1.)/(27./4.*energies[index_max:]**2),s = 1,color = "yellow",label = "$n = 5$")
ax.scatter(energies[index_max:],data_12[6,index_max:]*(np.exp(energies[index_max:]/T(M,Mstar,6)) + 1.)/(27./4.*energies[index_max:]**2),s = 1,color = "orange",label = "$n = 6$")

limit = 0.
for i in range(index_max,len(data_12[0])):
    limit += data_12_comp[i]*(np.exp(energies[i]/T(M,Mstar,0))-1.)/(27./4.*energies[i]**2)
limit = limit/(len(data_12[0])-index_max)

limits = np.zeros(6)
for j in range(6):
    for i in range(index_max,len(data_12[0])):
        limits[j] += data_12[1+j,i]*(np.exp(energies[i]/T(M,Mstar,j+1))-1.)/(27./4.*energies[i]**2)
    limits[j] = limits[j]/(len(data_12[0])-index_max)

ax.axhline(y = limit,xmin = 0,xmax = 1,linewidth = 0.5,color = "black")
ax.axhline(y = limits[0],xmin = 0,xmax = 1,linewidth = 0.5,color = "blue")
ax.axhline(y = limits[1],xmin = 0,xmax = 1,linewidth = 0.5,color = "green")
ax.axhline(y = limits[2],xmin = 0,xmax = 1,linewidth = 0.5,color = "red")
ax.axhline(y = limits[3],xmin = 0,xmax = 1,linewidth = 0.5,color = "purple")
ax.axhline(y = limits[4],xmin = 0,xmax = 1,linewidth = 0.5,color = "yellow")
ax.axhline(y = limits[5],xmin = 0,xmax = 1,linewidth = 0.5,color = "orange")

ax.legend(loc = "best")
f.show()

spin_12_path = "blah"

print("%15s%15s%15s%15s\n"%("n/fits","a1","b1","limit"),file=open(spin_12_path,"w"),end = "")
print("%15i%15.5e%15.5e%15.5e\n"%(0,slope,intercept,limit),file=open(spin_12_path,"a"),end = "")
for i in range(6):
    print("%15i%15.5e%15.5e%15.5e\n"%(i+1,slopes[i],intercepts[i],limits[i]),file=open(spin_12_path,"a"),end = "")

## Comparison to theory for all spins, high energy
fig_width_pt = 85*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 10,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 10,
          'legend.fontsize': 10,
          'xtick.labelsize': 10,
          'ytick.labelsize': 10,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

f = pylab.figure(2)
f.clf()
ax = f.add_subplot(111)

fits0 = np.genfromtxt("blah",skip_header = 1)
fits1 = np.genfromtxt("blah",skip_header = 1)
fits2 = np.genfromtxt("blah",skip_header = 1)
fits12 = np.genfromtxt("blah",skip_header = 1)

n = np.linspace(0,6,100)
ratio = np.zeros(len(n))
for i in range(len(n)):
    ratio[i] = rH(M,Mstar,n[i])**2*((n[i]+3)/2)**(2/(n[i]+1))*(n[i]+3)/(n[i]+1)

ax.set_xlabel("$n$")
ax.set_ylabel("$\\beta_\infty^n$")
ax.set_xlim(0,6)
ax.scatter(fits0[:,0],fits0[:,3],s = 10,marker = "+",color = "black",label = "${\\rm spin\,\,0}$")
ax.scatter(fits1[:,0],fits1[:,3],s = 10,marker = "x",color = "black",label = "${\\rm spin\,\,1}$")
ax.scatter(fits2[:,0],fits2[:,3],s = 10,marker = "o",color = "black",label = "${\\rm spin\,\,2}$")
ax.scatter(fits12[:,0],fits12[:,3],s = 10,marker = "s",color = "black",label = "${\\rm spin\,\,1/2}$")
ax.plot(n,ratio/27.*4.,color = 'red',linewidth = 1,label = "${\\rm theory}$")
ax.legend(loc = "best")

f.tight_layout(rect = [0,0,1,1])
f.show()
f.savefig("blah")

## Comparison between all spins for low energy
fig_width_pt = 85*2.83465  # Get this from LaTeX using \showthe\columnwidth
inches_per_pt = 1.0/72.27               # Convert pt to inch
fig_width = fig_width_pt*inches_per_pt  # width in inches
fig_height_pt = 80*2.83465
fig_height = fig_height_pt*inches_per_pt     # height in inches
fig_size =  [fig_width,fig_height]
params = {'backend': 'ps',
          'axes.labelsize': 10,
          'axes.linewidth': 0.5,
          #'text.fontsize': 11,
          'figure.titlesize': 10,
          'legend.fontsize': 10,
          'xtick.labelsize': 10,
          'ytick.labelsize': 10,
          'text.usetex': True,
          'figure.figsize': fig_size}
pylab.rcParams.update(params)

n = np.linspace(0,6,100)
beta_0 = np.zeros(100)
beta_1 = np.zeros(100)
beta_12 = np.zeros(100)
beta_2 = np.zeros(100)
for i in range(len(n)):
    beta_0[i] = 4.*rH(M,Mstar,n[i])**2
    beta_1[i] = 16./3./(n[i]+1.)**2*(gamma(1./(n[i]+1.))*gamma(2./(n[i]+1.))/gamma(3./(n[i]+1.)))**2*rH(M,Mstar,n[i])**4
    beta_12[i] = 8.*2.**(-4./(n[i]+1))*rH(M,Mstar,n[i])**2
    beta_2[i] =512*np.pi/1125*rH(M,Mstar,n[i])**6*gamma(1./(n[i]+1))**2*gamma(4./(n[i]+1))**2/gamma(5./(n[i]+1))**2/(n[i]+1)**2

f = pylab.figure(5)
f.clf()
ax = f.add_subplot(111)
ax.set_xscale("linear")
ax.set_yscale("log")
ax.set_xlim(0,6)
ax.set_ylim(1e-2,10)
ax.set_xlabel("$n$")
ax.set_ylabel("$\\beta_s^n$")
ax.scatter(fits0[:,0],10**fits0[:,2]/4.,marker = "+",s = 10,color = "blue",label = "${\\rm spin\,\,0}$")
ax.plot(n,beta_0/4.,linewidth = 1,color = "blue")
ax.scatter(fits1[:,0],10**fits1[:,2]*3./4.,marker = "x",s = 10,color = "green",label = "${\\rm spin\,\,1}$")
ax.plot(n,beta_1*3./4.,linewidth = 1,color = "green")
ax.scatter(fits2[:,0],10**fits2[:,2]*45./4.,marker = "o",s = 10,color = "red",label = "${\\rm spin\,\,2}$")
#ax.plot(n,np.linspace(100,100,len(n)),color = "red",linewidth = 1)
ax.plot(n,beta_2*45./4.,color = "red",linewidth = 1)
ax.scatter(fits12[:,0],10**fits12[:,2]*2.,marker = "s",s = 10,color = "purple",label = "${\\rm spin\,\,1/2}$")
ax.plot(n,beta_12*2.,linewidth = 1,color = "purple")
ax.legend(loc = 4,ncol = 1)
f.tight_layout(rect = [0,0,1,1])
f.show()
f.savefig("blah")


##
pylab.close("all")