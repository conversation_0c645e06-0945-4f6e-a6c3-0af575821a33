				#####################
				#		            #
				# BlackHawk scripts #
				#		            #
				#####################



The script in this folder is designed to compute the Hazma hadronization tables
for final particles photons and electrons, valid at low energy.

author : <PERSON>�<PERSON>, jere<PERSON>.<EMAIL> & <PERSON>, <EMAIL>
last modified : 07 April 2024

#########################################################################################

This folder contains 1 file:

- hazma_tables.py which is a Python script to use in congruence with the Python package of
Hazma (see details in <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. <PERSON>, JCAP 01, 056, arXiv:1907.11846 [hep-ph]
and download the package at https://github.com/LoganAMorrison/Hazma with detailed documentation at
https://hazma.readthedocs.io/)

##########################################################################################

If you have any issue using these scripts please feel free to contact the authors.























