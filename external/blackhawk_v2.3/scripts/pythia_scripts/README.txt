				#####################
				#		            #
				# BlackHawk scripts #
				#		            #
				#####################



The scripts in this folder are designed to compute the PYTHIA hadronization tables
for final particles corresponding to BBN epoch (lifetime > 10^(-8) s).

author : <PERSON>, alexand<PERSON>.<EMAIL> & <PERSON>�<PERSON>, jere<PERSON>.<EMAIL>
last modified : 07 April 2024

#########################################################################################

This folder contains 3 types of files:

- table_*.cc are C files used to compute the hadronization table of the particle *

- formatting.c is a C file used to format the computed tables

- Makefile is a compilation file

#########################################################################################

To use the .cc scripts, you will need PYTHIA (version 8 or later), that you can find here:

	http://home.thep.lu.se/Pythia/

Follow the instructions of the PYTHIA webpage for the installation.

Then, simply go into:

	/pythia****/examples/

and copy the .cc scripts there. PYTHIA has a built-in Makefile in the /examples folder.
You have to add a rule to compile the table_*.cc files, on the same model as the "main"
rules used to compile the main*.cc examples.

To compile the scripts, simply type:

	make table_*

without the .cc extension. This will create executable files table_* without extension.

To launch the scripts, type:

	./table_*

We recommand to add a "> output" to this command because of the extensive PYTHIA output.
Several scripts can be launched in parallel.

#########################################################################################

These scripts will create table_*.txt output files, one per initial particle. In these
files, the first column is the initial total energy of the initial particle, the second 
column is the finaltotal energy of the final particle (both are in GeV), and each other
column is the corresponding mean number of the corresponding final particle generated by
the corresponding reaction in the energy interval around the corresponding final energy.

#########################################################################################

To be usable by BlackHawk, these tables need to be formatted in the opposite way, that is
to say one file *.txt per final particle, in which each column is an initial particle
type. This is the purpose of formatting.c. To use this script, simply move your tables
table_*.txt to another folder where you have put formating.c and Makefile, and type:

	make

This will create an executable formatting.x. Launch it by typing:

	./formatting.x

This will read the table_*.txt tables and create the new *.txt tables.

#########################################################################################

The parameters of these scripts are the following:

- nb_initial_energies / nb_init_en : number of initial energies for the initial particles

- nb_final_energies / nb_fin_en : number of final energies for the final particles

- nb_initial_particles / nb_init_part : number of initial particle types

	14 : bottom, charm, down, electron, gluons, higgs, muon, photon, strange, tau, top,
	     up, W+-, Z0

- nb_final_particles / nb_fin_part : number of final particle types

	11 : photon, electron, muon, nu_e, nu_mu, nu_tau, pi+-, K+-, K0 long, proton,
	     neutron

- Emin_init / Emax_init : initial energy boundaries (in GeV)
	
	Emin_init should not be less than 5 GeV as it is a limit for PYTHIA.
	Emax_init could be up to some 10^2 TeV.
	For more informations see the PYTHIA docs.

- Emin_fin / Emax_fin : final energy boundaries (in GeV)

Please make sur that the parameters of table_*.cc and formatting.c correspond in order to
avoid segmentation faults.

##########################################################################################

If you have any issue using these scripts please feel free to contact the authors.























