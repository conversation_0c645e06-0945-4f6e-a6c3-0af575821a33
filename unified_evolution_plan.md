# 黑洞霍金辐射+超辐射统一演化程序整合计划

## 第一阶段：接口设计

### 1.1 QNM计算接口（C调用Julia）
```c
// qnm_interface.h
typedef struct {
    double omega_real;    // 实部频率
    double omega_imag;    // 虚部频率  
    double lambda;        // 角本征值
    int converged;        // 收敛标志
} QNMResult;

QNMResult calculate_qnm(double M, double a, double mu, int l, int m, int n);
int check_superradiance(double omega_real, double m, double omega_horizon);
```

### 1.2 超辐射率计算
```c
// superradiance.h
typedef struct {
    double growth_rate;      // 质量增长率
    double spin_change_rate; // 自旋变化率
    double amplitude;        // 超辐射振幅
} SuperradianceRate;

SuperradianceRate compute_superradiance_rate(
    double M, double a, double mu_scalar, 
    QNMResult qnm, int l, int m
);
```

## 第二阶段：演化方程修改

### 2.1 扩展演化方程
原BlackHawk演化方程：
- dM/dt = -f(M,a)/M²  (霍金辐射)
- da/dt = -g(M,a)a/M³ + 2f(M,a)a/M³

新的统一演化方程：
```c
dM/dt = dM/dt_Hawking + dM/dt_Superradiance
da/dt = da/dt_Hawking + da/dt_Superradiance
```

### 2.2 演化函数实现
```c
typedef struct {
    double M;           // 黑洞质量
    double a;           // 无量纲自旋
    double Q;           // 电荷（如需要）
    double mu_cloud;    // 标量场云质量
    double rho_cloud;   // 标量场云密度
} BlackHoleState;

void evolve_unified(
    BlackHoleState *bh,
    double dt,
    HawkingTables *hawking,
    SuperradianceParams *super
);
```

## 第三阶段：数值实现细节

### 3.1 Julia接口实现
使用Julia C API或通过系统调用：
```c
// 方案1：系统调用
QNMResult call_julia_qnm(double M, double a, double mu, int l, int m) {
    char command[512];
    sprintf(command, "julia qnm_wrapper.jl %f %f %f %d %d", M, a, mu, l, m);
    // 执行并解析结果
}

// 方案2：嵌入Julia（更高效）
#include <julia.h>
void init_julia_qnm() {
    jl_init();
    jl_eval_string("include(\"Massive_QNMs_Kerr_BH.jl\")");
}
```

### 3.2 自适应时间步长
```c
double adaptive_timestep(BlackHoleState *bh, double dt_current) {
    double dt_hawking = estimate_hawking_timescale(bh);
    double dt_super = estimate_superradiance_timescale(bh);
    double dt_new = fmin(dt_hawking, dt_super) * 0.1;
    
    // 限制时间步长变化
    if (dt_new > 2.0 * dt_current) dt_new = 2.0 * dt_current;
    if (dt_new < 0.5 * dt_current) dt_new = 0.5 * dt_current;
    
    return dt_new;
}
```

## 第四阶段：参数文件扩展

### 4.1 新增参数
```
# 超辐射相关参数
enable_superradiance = 1        # 是否开启超辐射
scalar_mass = 1e-12            # 标量场质量 (eV)
l_max = 5                      # 最大角量子数
m_max = 5                      # 最大磁量子数
initial_cloud_fraction = 1e-6  # 初始标量云质量分数
qnm_precision = 1e-10          # QNM计算精度
```

## 第五阶段：输出扩展

### 5.1 新增输出文件
- `superradiance_rates.txt`: 超辐射率随时间演化
- `cloud_evolution.txt`: 标量场云演化
- `qnm_modes.txt`: 主导QNM模式信息
- `energy_balance.txt`: 能量守恒检验

### 5.2 诊断输出
```c
typedef struct {
    double total_energy_radiated;    // 霍金辐射总能量
    double total_energy_absorbed;    // 超辐射吸收总能量
    double energy_conservation_error; // 能量守恒误差
} DiagnosticInfo;
```

## 第六阶段：验证测试

### 6.1 单元测试
- QNM计算验证（与文献对比）
- 超辐射率计算验证
- 能量守恒检验
- 极限情况测试（a→0, μ→0）

### 6.2 集成测试
- 霍金辐射主导情况（高温黑洞）
- 超辐射主导情况（低温黑洞+轻标量场）
- 竞争情况（两者可比）
- 长时间演化稳定性

## 实施时间表

1. **第1-2周**：完成接口设计和基础数据结构
2. **第3-4周**：实现Julia-C接口和QNM计算集成
3. **第5-6周**：修改演化方程，集成超辐射计算
4. **第7-8周**：完成自适应时间步长和数值稳定性
5. **第9-10周**：扩展输入输出，添加诊断功能
6. **第11-12周**：全面测试和优化

## 关键技术难点

1. **Julia-C接口效率**：考虑缓存QNM结果或预计算表格
2. **数值稳定性**：超辐射时标可能远长于霍金辐射
3. **模式选择**：确定哪些(l,m,n)模式需要计算
4. **初始条件**：标量场云的初始配置
5. **自洽性**：云质量反作用于黑洞度规

## 优化建议

1. **并行计算**：不同(l,m)模式可并行计算
2. **查表法**：对常用参数范围预计算QNM
3. **稀疏演化**：仅在超辐射显著时计算
4. **自适应精度**：根据演化阶段调整计算精度 